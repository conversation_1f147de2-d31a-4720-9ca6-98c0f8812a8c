<template>
  <div class="code-snippet-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>Code snippet</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="language-selector">
        <el-select v-model="selectedLanguage" @change="generateCodeSnippet" size="mini" style="width: 100%">
          <el-option label="Shell - cURL" value="shell-curl"></el-option>
          <el-option label="JavaScript - Fetch" value="javascript-fetch"></el-option>
          <el-option label="Python - Requests" value="python-requests"></el-option>
          <el-option label="PHP - cURL" value="php-curl"></el-option>
          <el-option label="Go - HTTP" value="go-http"></el-option>
          <el-option label="Java - HTTP" value="java-http"></el-option>
        </el-select>
      </div>
    </div>

    <div class="code-section">
      <div class="section-title">
        <span>Generated code</span>
        <div class="section-actions">
          <el-tooltip content="Format code" placement="top">
            <el-button size="mini" icon="el-icon-document-copy" @click="formatCode"></el-button>
          </el-tooltip>
          <el-tooltip content="Download" placement="top">
            <el-button size="mini" icon="el-icon-download" @click="downloadCode"></el-button>
          </el-tooltip>
          <el-tooltip content="Copy" placement="top">
            <el-button size="mini" icon="el-icon-document" @click="copyCode"></el-button>
          </el-tooltip>
        </div>
      </div>

      <div class="code-editor">
        <el-input v-model="generatedCode" type="textarea" :rows="10" readonly class="code-textarea" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CodeSnippetPanel',
  props: {
    currentRequest: {
      type: Object,
      default: () => ({
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login'
      })
    }
  },
  data() {
    return {
      selectedLanguage: 'shell-curl',
      generatedCode: ''
    }
  },
  computed: {
    codeLines() {
      return this.generatedCode.split('\n');
    }
  },
  watch: {
    currentRequest: {
      handler() {
        this.generateCodeSnippet();
      },
      deep: true
    }
  },
  mounted() {
    this.generateCodeSnippet();
  },
  methods: {
    generateCodeSnippet() {
      const { method, url } = this.currentRequest;

      switch (this.selectedLanguage) {
        case 'shell-curl':
          this.generatedCode = `curl --request ${method} \\\n--url ${url}`;
          break;
        case 'javascript-fetch':
          this.generatedCode = `fetch('${url}', {\n  method: '${method}',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n.then(response => response.json())\n.then(data => console.log(data))\n.catch(error => console.error('Error:', error));`;
          break;
        case 'python-requests':
          this.generatedCode = `import requests\n\nresponse = requests.${method.toLowerCase()}('${url}')\nprint(response.json())`;
          break;
        case 'php-curl':
          this.generatedCode = `<?php\n$ch = curl_init();\ncurl_setopt($ch, CURLOPT_URL, '${url}');\ncurl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\ncurl_setopt($ch, CURLOPT_CUSTOMREQUEST, '${method}');\n$response = curl_exec($ch);\ncurl_close($ch);\necho $response;`;
          break;
        case 'go-http':
          this.generatedCode = `package main\n\nimport (\n    "fmt"\n    "io/ioutil"\n    "net/http"\n)\n\nfunc main() {\n    resp, err := http.Get("${url}")\n    if err != nil {\n        panic(err)\n    }\n    defer resp.Body.Close()\n    body, err := ioutil.ReadAll(resp.Body)\n    fmt.Println(string(body))\n}`;
          break;
        case 'java-http':
          this.generatedCode = `import java.net.http.*;\nimport java.net.URI;\n\nHttpClient client = HttpClient.newHttpClient();\nHttpRequest request = HttpRequest.newBuilder()\n    .uri(URI.create("${url}"))\n    .method("${method}", HttpRequest.BodyPublishers.noBody())\n    .build();\nHttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());\nSystem.out.println(response.body());`;
          break;
        default:
          this.generatedCode = `# ${this.selectedLanguage} code for ${method} ${url}`;
      }
    },
    highlightSyntax(line) {
      // 简单的语法高亮
      return line
        .replace(/curl|fetch|requests|HttpClient/g, '<span class="keyword">$&</span>')
        .replace(/GET|POST|PUT|DELETE|PATCH/g, '<span class="method">$&</span>')
        .replace(/--request|--url|method|headers|Content-Type/g, '<span class="flag">$&</span>')
        .replace(/https?:\/\/[^\s'"]+/g, '<span class="url">$&</span>')
        .replace(/".*?"|'.*?'/g, '<span class="string">$&</span>');
    },
    formatCode() {
      // 格式化代码
      console.log('格式化代码');
    },
    downloadCode() {
      const blob = new Blob([this.generatedCode], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `request_${this.currentRequest.method.toLowerCase()}_${Date.now()}.${this.getFileExtension()}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    },
    copyCode() {
      navigator.clipboard.writeText(this.generatedCode).then(() => {
        this.$message.success('代码已复制到剪贴板');
      }).catch(err => {
        this.$message.error('复制失败: ' + err.message);
      });
    },
    getFileExtension() {
      const extensions = {
        'shell-curl': 'sh',
        'javascript-fetch': 'js',
        'python-requests': 'py',
        'php-curl': 'php',
        'go-http': 'go',
        'java-http': 'java'
      };
      return extensions[this.selectedLanguage] || 'txt';
    }
  }
}
</script>

<style scoped>
.code-snippet-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.language-selector {
  margin-bottom: 16px;
}


.code-section {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.code-editor {
  flex: 1;
}

.code-textarea>>>.el-textarea__inner {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  color: #333;
}

.code-textarea>>>.el-textarea__inner:focus {
  border-color: #409eff;
}
</style>
