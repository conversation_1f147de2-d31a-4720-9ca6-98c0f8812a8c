<template>
  <div class="environments-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>Environments</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="environment-header">
        <i class="el-icon-global globe-icon"></i>
        <span class="header-title">Global</span>
        <el-dropdown trigger="click" class="tree-actions">
          <span class="el-dropdown-link">
            <i class="el-icon-more"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>Settings</el-dropdown-item>
            <el-dropdown-item>Export</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <el-input v-model="searchQuery" placeholder="Search" prefix-icon="el-icon-search" size="mini"
        class="search-input" />

      <div class="action-buttons">
        <el-button type="primary" size="mini" @click="$emit('show-environment-modal')">
          <i class="el-icon-plus"></i> New
        </el-button>
        <div class="header-icons">
          <el-tooltip content="Folders" placement="bottom">
            <el-button icon="el-icon-folder" size="mini" circle></el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="empty-state" v-if="environments.length === 0">
      <div class="empty-icon">
        <i class="el-icon-aim cube-icon"></i>
      </div>
      <div class="empty-title">Environments are empty</div>
      <div class="empty-subtitle">Import or create an environment</div>
      <div class="empty-actions">
        <el-button type="success" @click="importEnvironment">
          <i class="el-icon-upload2"></i> Import
        </el-button>
        <el-button @click="$emit('show-environment-modal')">
          <i class="el-icon-plus"></i> Add new
        </el-button>
      </div>
    </div>

    <div v-else class="environments-list">
      <el-card v-for="env in filteredEnvironments" :key="env.id" class="environment-item" shadow="hover"
        @click.native="selectEnvironment(env)">
        <div class="env-info">
          <div class="env-name">{{ env.name }}</div>
          <div class="env-description">{{ env.description || 'No description' }}</div>
        </div>
        <div class="env-actions">
          <el-tooltip content="Edit" placement="top">
            <el-button icon="el-icon-edit" size="mini" circle @click.stop="editEnvironment(env)"></el-button>
          </el-tooltip>
          <el-tooltip content="Delete" placement="top">
            <el-button icon="el-icon-delete" size="mini" circle type="danger"
              @click.stop="deleteEnvironment(env.id)"></el-button>
          </el-tooltip>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnvironmentsPanel',
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      searchQuery: '',
      environments: [] // 初始为空数组，显示空状态
    }
  },
  computed: {
    filteredEnvironments() {
      if (!this.searchQuery) {
        return this.environments;
      }
      return this.environments.filter(env =>
        env.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  methods: {
    selectEnvironment(env) {
      console.log('选择环境:', env);
      // 这里应该触发环境选择事件
    },
    editEnvironment(env) {
      console.log('编辑环境:', env);
      // 这里应该触发编辑环境事件
    },
    deleteEnvironment(envId) {
      this.$confirm('确定要删除这个环境吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.environments.findIndex(env => env.id === envId);
        if (index > -1) {
          this.environments.splice(index, 1);
          this.$message.success('删除成功');
        }
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    importEnvironment() {
      // 创建文件输入元素
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            try {
              const data = JSON.parse(e.target.result);
              console.log('导入环境数据:', data);
              this.$message.success('环境导入成功！');
              // 这里应该将导入的数据添加到environments数组中
            } catch (error) {
              this.$message.error('文件格式错误，请选择有效的JSON文件');
            }
          };
          reader.readAsText(file);
        }
      };
      input.click();
    }
  }
}
</script>

<style scoped>
.environments-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  margin-bottom: 12px;
}

.environment-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.globe-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #409eff;
}

.header-title {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.tree-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.environment-header:hover .tree-actions {
  opacity: 1;
}

.el-dropdown-link {
  cursor: pointer;
  color: #666;
  font-size: 12px;
}

.el-dropdown-link:hover {
  color: #409eff;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 20px;
}

.cube-icon {
  font-size: 64px;
  opacity: 0.6;
  color: #c0c4cc;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

.empty-actions {
  display: flex;
  gap: 12px;
}

.environments-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.environment-item {
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.environment-item:hover {
  transform: translateY(-2px);
}

.environment-item>>>.el-card__body {
  padding: 12px;
  display: flex;
  align-items: center;
}

.env-info {
  flex: 1;
}

.env-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.env-description {
  font-size: 12px;
  color: #666;
}

.env-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.environment-item:hover .env-actions {
  opacity: 1;
}
</style>
