<template>
  <div class="headers-tab">
    <div class="headers-section">
      <div class="section-header">
        <h4 class="section-title">Headers</h4>
        <div class="header-actions">
          <el-button size="mini" type="text" @click="addCommonHeaders" class="action-btn">
            <i class="el-icon-plus"></i> Common
          </el-button>
        </div>
      </div>

      <div class="param-table">
        <div class="param-header">
          <div class="param-col param-col-enabled">
            <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" size="mini">
            </el-checkbox>
          </div>
          <div class="param-col param-col-key">Key</div>
          <div class="param-col param-col-value">Value</div>
          <div class="param-col param-col-description">Description</div>
          <div class="param-col param-col-actions">Actions</div>
        </div>

        <div v-for="(header, index) in headers" :key="index" class="param-row"
          :class="{ 'disabled-row': !header.enabled }">
          <div class="param-col param-col-enabled">
            <el-checkbox v-model="header.enabled" @change="handleHeaderChange" size="mini"></el-checkbox>
          </div>
          <div class="param-col param-col-key">
            <el-autocomplete v-model="header.key" :fetch-suggestions="queryHeaderSuggestions" placeholder="Header name"
              size="mini" class="header-key-input" @input="updateHeaders" />
          </div>
          <div class="param-col param-col-value">
            <el-input v-model="header.value" placeholder="Header value" size="mini" @input="updateHeaders" />
          </div>
          <div class="param-col param-col-description">
            <el-input v-model="header.description" placeholder="Description" size="mini" />
          </div>
          <div class="param-col param-col-actions">
            <el-button icon="el-icon-delete" size="mini" type="text" @click="removeHeader(index)" class="delete-btn" />
          </div>
        </div>

        <div class="param-row add-row">
          <el-button icon="el-icon-plus" size="mini" type="text" @click="addHeader" class="add-btn">
            Add Header
          </el-button>
        </div>
      </div>
    </div>

    <!-- 常用请求头快捷添加 -->
    <el-dialog title="Add Common Headers" :visible.sync="showCommonHeaders" width="500px" class="common-headers-dialog">
      <div class="common-headers-list">
        <div v-for="(header, key) in commonHeaders" :key="key" class="common-header-item">
          <el-checkbox v-model="header.selected" size="mini">
            <div class="header-info">
              <span class="header-name">{{ key }}</span>
              <span class="header-example">{{ header.example }}</span>
            </div>
          </el-checkbox>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCommonHeaders = false" size="mini">Cancel</el-button>
        <el-button type="primary" @click="addSelectedHeaders" size="mini">Add Selected</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'HeadersTab',
  data() {
    return {
      headers: [
        { key: '', value: '', description: '', enabled: true }
      ],
      checkAll: false,
      isIndeterminate: false,
      showCommonHeaders: false,
      commonHeaders: {
        'Content-Type': { example: 'application/json', selected: false },
        'Accept': { example: 'application/json', selected: false },
        'Authorization': { example: 'Bearer <token>', selected: false },
        'User-Agent': { example: 'MyApp/1.0', selected: false },
        'Cache-Control': { example: 'no-cache', selected: false },
        'X-Requested-With': { example: 'XMLHttpRequest', selected: false }
      },
      headerSuggestions: [
        'Accept', 'Accept-Encoding', 'Accept-Language', 'Authorization',
        'Cache-Control', 'Connection', 'Content-Length', 'Content-Type',
        'Cookie', 'Host', 'Origin', 'Referer', 'User-Agent', 'X-Requested-With'
      ]
    }
  },
  methods: {
    addHeader() {
      this.headers.push({ key: '', value: '', description: '', enabled: true });
      this.updateCheckAllStatus();
    },
    removeHeader(index) {
      if (this.headers.length > 1) {
        this.headers.splice(index, 1);
        this.updateCheckAllStatus();
      }
    },
    updateHeaders() {
      this.$emit('update-headers', this.headers.filter(h => h.enabled && h.key));
    },
    handleCheckAllChange(val) {
      this.headers.forEach(header => {
        header.enabled = val;
      });
      this.isIndeterminate = false;
      this.updateHeaders();
    },
    handleHeaderChange() {
      this.updateCheckAllStatus();
      this.updateHeaders();
    },
    updateCheckAllStatus() {
      const enabledCount = this.headers.filter(h => h.enabled).length;
      this.checkAll = enabledCount === this.headers.length;
      this.isIndeterminate = enabledCount > 0 && enabledCount < this.headers.length;
    },
    queryHeaderSuggestions(queryString, cb) {
      const suggestions = this.headerSuggestions
        .filter(header => header.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
        .map(header => ({ value: header }));
      cb(suggestions);
    },
    addCommonHeaders() {
      Object.keys(this.commonHeaders).forEach(key => {
        this.commonHeaders[key].selected = false;
      });
      this.showCommonHeaders = true;
    },
    addSelectedHeaders() {
      Object.keys(this.commonHeaders).forEach(key => {
        if (this.commonHeaders[key].selected) {
          const exists = this.headers.some(h => h.key.toLowerCase() === key.toLowerCase());
          if (!exists) {
            this.headers.push({
              key: key,
              value: this.commonHeaders[key].example,
              description: '',
              enabled: true
            });
          }
        }
      });
      this.showCommonHeaders = false;
      this.updateCheckAllStatus();
      this.updateHeaders();
    }
  },
  mounted() {
    this.updateCheckAllStatus();
  }
}
</script>

<style scoped>
.headers-tab {
  padding: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 6px;
}

.action-btn {
  color: #1890ff;
  font-size: 11px;
  padding: 2px 6px;
  height: 22px;
  line-height: 18px;
}

.action-btn:hover {
  color: #40a9ff;
  background-color: rgba(24, 144, 255, 0.06);
}

/* 紧凑的参数表格样式 */
.param-table {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.param-header {
  display: flex;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  font-size: 11px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s ease;
  min-height: 36px;
}

.param-row:hover {
  background-color: rgba(24, 144, 255, 0.02);
}

.param-row.disabled-row {
  opacity: 0.6;
  background-color: #f9f9f9;
}

.param-col {
  padding: 6px 8px;
  display: flex;
  align-items: center;
}

.param-col-enabled {
  flex: 0 0 32px;
  border-right: 1px solid #f5f5f5;
  justify-content: center;
}

.param-col-key {
  flex: 0 0 22%;
  border-right: 1px solid #f5f5f5;
}

.param-col-value {
  flex: 0 0 30%;
  border-right: 1px solid #f5f5f5;
}

.param-col-description {
  flex: 1;
  border-right: 1px solid #f5f5f5;
}

.param-col-actions {
  flex: 0 0 50px;
  justify-content: center;
}

.add-row {
  background: #fafafa;
  padding: 8px 12px;
  justify-content: flex-start;
  border-bottom: none;
}

.add-btn {
  color: #1890ff;
  font-size: 11px;
  padding: 0;
  height: 20px;
  line-height: 20px;
}

.delete-btn {
  color: #f56c6c;
  padding: 2px;
  width: 20px;
  height: 20px;
  font-size: 12px;
}

/* 常用请求头对话框紧凑样式 */
.common-headers-list {
  max-height: 300px;
  overflow-y: auto;
}

.common-header-item {
  padding: 6px 0;
  border-bottom: 1px solid #f5f5f5;
}

.common-header-item:last-child {
  border-bottom: none;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.header-name {
  font-weight: 600;
  color: #303133;
  font-size: 12px;
}

.header-example {
  font-size: 10px;
  color: #909399;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
}

/* 自动完成输入框 */
.header-key-input {
  width: 100%;
}

.headers-tab>>>.el-input--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
  font-size: 11px;
  border: none;
  background: transparent;
  padding: 0 6px;
}

.headers-tab>>>.el-input--mini .el-input__inner:focus {
  border: 1px solid #1890ff;
  background: white;
  border-radius: 3px;
}

.headers-tab>>>.el-autocomplete {
  width: 100%;
}

.headers-tab>>>.el-checkbox {
  margin-right: 0;
}

.headers-tab>>>.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}

.headers-tab>>>.el-checkbox--mini .el-checkbox__inner {
  width: 12px;
  height: 12px;
}

.headers-tab>>>.el-checkbox--mini .el-checkbox__inner::after {
  width: 3px;
  height: 6px;
  left: 3px;
  top: 1px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .param-col-enabled {
    flex: 0 0 28px;
  }

  .param-col-key {
    flex: 0 0 25%;
  }

  .param-col-value {
    flex: 0 0 35%;
  }

  .param-col-actions {
    flex: 0 0 40px;
  }
}
</style>
