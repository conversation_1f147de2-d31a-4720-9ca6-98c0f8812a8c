<template>
  <div class="history-panel">
    <div class="left-header">
      <div class="breadcrumb">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item>History</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <el-input v-model="searchQuery" placeholder="Search history" prefix-icon="el-icon-search" size="mini"
        class="search-input" />

      <div class="action-buttons">
        <el-button type="danger" size="mini" @click="clearHistory">
          <i class="el-icon-delete"></i> Clear
        </el-button>
        <div class="header-icons">
          <el-tooltip content="Help" placement="bottom">
            <el-button icon="el-icon-question" size="mini" circle></el-button>
          </el-tooltip>
          <el-tooltip content="Export" placement="bottom">
            <el-button icon="el-icon-upload2" size="mini" circle></el-button>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div class="history-list" v-if="historyItems.length > 0">
      <el-card v-for="item in filteredHistory" :key="item.id" class="history-item" shadow="hover"
        @click.native="selectHistoryItem(item)">
        <div class="history-info">
          <el-tag :type="getMethodTagType(item.method)" size="mini" class="method-tag">
            {{ item.method }}
          </el-tag>
          <div class="request-details">
            <div class="request-name">{{ item.name }}</div>
            <div class="request-url">{{ item.url }}</div>
            <div class="request-time">
              <i class="el-icon-time"></i>
              {{ formatTime(item.timestamp) }}
            </div>
          </div>
        </div>
        <div class="history-status">
          <el-tag :type="item.status === 'success' ? 'success' : 'danger'" size="mini" class="status-tag">
            <i :class="item.status === 'success' ? 'el-icon-success' : 'el-icon-error'"></i>
            {{ item.status === 'success' ? 'Success' : 'Failed' }}
          </el-tag>
        </div>
      </el-card>
    </div>

    <div v-else class="empty-history">
      <div class="empty-icon">
        <i class="el-icon-time"></i>
      </div>
      <div class="empty-text">No history yet</div>
      <div class="empty-subtext">Start making requests to see them here</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HistoryPanel',
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      searchQuery: '',
      historyItems: [
        {
          id: '1',
          method: 'GET',
          name: 'login',
          url: 'https://echo.hoppscotch.io/login',
          status: 'success',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
          duration: 245,
          size: '1.2KB'
        },
        {
          id: '2',
          method: 'POST',
          name: 'create user',
          url: 'https://echo.hoppscotch.io/users',
          status: 'success',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
          duration: 180,
          size: '800B'
        },
        {
          id: '3',
          method: 'GET',
          name: 'get profile',
          url: 'https://echo.hoppscotch.io/profile',
          status: 'error',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
          duration: 5000,
          size: '0B'
        }
      ]
    }
  },
  computed: {
    filteredHistory() {
      if (!this.searchQuery) {
        return this.historyItems;
      }
      return this.historyItems.filter(item =>
        item.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        item.url.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  methods: {
    selectHistoryItem(item) {
      console.log('选择历史记录:', item);
      // 这里应该触发选择历史记录事件，可能重新发送请求
    },
    clearHistory() {
      this.$confirm('确定要清除所有历史记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.historyItems = [];
        this.$message.success('历史记录已清除');
      }).catch(() => {
        this.$message.info('已取消清除');
      });
    },
    formatTime(timestamp) {
      const now = new Date();
      const diff = now - timestamp;

      if (diff < 1000 * 60) {
        return '刚刚';
      } else if (diff < 1000 * 60 * 60) {
        return `${Math.floor(diff / (1000 * 60))}分钟前`;
      } else if (diff < 1000 * 60 * 60 * 24) {
        return `${Math.floor(diff / (1000 * 60 * 60))}小时前`;
      } else {
        return timestamp.toLocaleDateString();
      }
    },
    getMethodTagType(method) {
      const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      };
      return types[method] || 'success';
    }
  }
}
</script>

<style scoped>
.history-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  margin-bottom: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.header-icons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.history-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-item:hover {
  transform: translateY(-2px);
}

.history-item>>>.el-card__body {
  padding: 12px;
  display: flex;
  align-items: center;
}

.history-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.method-tag {
  margin-right: 12px;
}

.request-details {
  flex: 1;
}

.request-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.request-url {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.request-time {
  font-size: 11px;
  color: #999;
  display: flex;
  align-items: center;
  gap: 4px;
}

.history-status {
  margin-left: 12px;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

.empty-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.6;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #666;
}
</style>
