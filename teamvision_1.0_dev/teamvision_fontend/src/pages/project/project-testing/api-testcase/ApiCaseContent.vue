<template>
  <div class="api-case-content">
    <!-- 请求标签页栏 -->
    <el-tabs v-model="activeRequestId" type="card" @tab-remove="closeRequestTab" @tab-click="handleTabClick"
      size="mini">
      <el-tab-pane v-for="request in requestTabs" :key="request.id" :label="request.name" :name="request.id"
        :closable="true">
        <span slot="label">
          <el-tag :type="getMethodTagType(request.method)" size="mini" class="method-tag">
            {{ request.method }}
          </el-tag>
          {{ request.name }}
        </span>

        <!-- 每个标签页的完整内容 -->
        <div class="tab-pane-content">
          <!-- 请求构建区域 -->
          <div class="request-builder">
            <div class="request-header">
              <el-select v-model="currentRequestData.method" class="method-select" size="mini" @change="updateRequest">
                <el-option label="GET" value="GET"></el-option>
                <el-option label="POST" value="POST"></el-option>
                <el-option label="PUT" value="PUT"></el-option>
                <el-option label="DELETE" value="DELETE"></el-option>
                <el-option label="PATCH" value="PATCH"></el-option>
              </el-select>

              <el-input v-model="currentRequestData.url" placeholder="Enter request URL" class="url-input"
                @input="updateRequest" size="mini" />

              <div class="action-buttons-right">
                <el-dropdown @command="handleSendCommand" size="mini">
                  <el-button type="primary" :loading="isLoading" size="mini">
                    <i class="el-icon-right"></i> Send
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="send">Send Request</el-dropdown-item>
                    <el-dropdown-item command="send-and-save">Send and Save</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>

                <el-dropdown @command="handleSaveCommand" size="mini">
                  <el-button size="mini">
                    <i class="el-icon-document"></i> Save
                    <i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="save">Save Request</el-dropdown-item>
                    <el-dropdown-item command="save-as">Save As...</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 请求内容区域 -->
          <div class="request-content-area">
            <!-- 选项卡 -->
            <el-tabs v-model="activeTab" size="mini" class="content-tabs">
              <el-tab-pane label="Parameters" name="parameters">
                <parameters-tab />
              </el-tab-pane>

              <el-tab-pane label="Body" name="body">
                <body-tab />
              </el-tab-pane>

              <el-tab-pane label="Headers" name="headers">
                <headers-tab />
              </el-tab-pane>

              <el-tab-pane label="Authorization" name="authorization">
                <authorization-tab />
              </el-tab-pane>

              <el-tab-pane label="Pre-request Script" name="pre-script">
                <pre-script-tab />
              </el-tab-pane>

              <el-tab-pane label="Post-request Script" name="post-script">
                <post-script-tab />
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 响应区域 -->
          <response-area v-if="showResponse && activeRequestId === request.id" :response="responseData" />
        </div>
      </el-tab-pane>

      <!-- 新建请求标签页 -->
      <el-tab-pane name="add-new" :closable="false">
        <span slot="label" @click.stop="createNewRequest" class="add-new-tab">
          <i class="el-icon-plus"></i>
        </span>
      </el-tab-pane>

      <!-- 选择环境标签页 -->
      <el-tab-pane name="environment" :closable="false">
        <span slot="label" class="environment-tab" @click.stop>
          <el-dropdown @command="handleEnvironmentCommand" size="mini" trigger="click">
            <span class="env-selector">
              <i class="el-icon-document"></i>
              <span>{{ selectedEnvironment || 'Select environment' }}</span>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="select">选择环境</el-dropdown-item>
              <el-dropdown-item command="manage">管理环境</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-tooltip content="View environment details" placement="bottom">
            <el-button icon="el-icon-view" size="mini" circle class="env-view-btn" @click.stop></el-button>
          </el-tooltip>
        </span>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ParametersTab from './components/tabs/ParametersTab.vue';
import BodyTab from './components/tabs/BodyTab.vue';
import HeadersTab from './components/tabs/HeadersTab.vue';
import AuthorizationTab from './components/tabs/AuthorizationTab.vue';
import PreScriptTab from './components/tabs/PreScriptTab.vue';
import PostScriptTab from './components/tabs/PostScriptTab.vue';
import ResponseArea from './components/ResponseArea.vue';

export default {
  name: 'ApiCaseContent',
  props: {
    projectID: {
      type: String,
      required: true
    },
    currentRequest: {
      type: Object,
      default: () => ({
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login',
        name: 'login'
      })
    }
  },
  data() {
    return {
      activeRequestId: 'login',
      activeTab: 'parameters',
      showResponse: false,
      isLoading: false,
      selectedEnvironment: null,
      requestTabs: [
        {
          id: 'login',
          method: 'GET',
          name: 'login',
          url: 'https://echo.hoppscotch.io/login'
        }
      ],
      currentRequestData: {
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login',
        name: 'login'
      },
      responseData: null
    }
  },
  methods: {
    switchRequest(requestId) {
      this.activeRequestId = requestId;
      const request = this.requestTabs.find(r => r.id === requestId);
      if (request) {
        this.currentRequestData = { ...request };
        this.$emit('update-request', this.currentRequestData);
      }
    },
    createNewRequest() {
      const newId = 'request_' + Date.now();
      const newRequest = {
        id: newId,
        method: 'GET',
        name: 'New Request',
        url: ''
      };
      this.requestTabs.push(newRequest);
      this.switchRequest(newId);
    },
    closeRequestTab(requestId) {
      const index = this.requestTabs.findIndex(r => r.id === requestId);
      if (index > -1) {
        this.requestTabs.splice(index, 1);
        if (this.activeRequestId === requestId && this.requestTabs.length > 0) {
          this.switchRequest(this.requestTabs[this.requestTabs.length - 1].id);
        }
      }
    },
    switchTab(tabKey) {
      this.activeTab = tabKey;
    },
    updateRequest() {
      const request = this.requestTabs.find(r => r.id === this.activeRequestId);
      if (request) {
        request.method = this.currentRequestData.method;
        request.url = this.currentRequestData.url;
        request.name = this.currentRequestData.name;
      }
      this.$emit('update-request', this.currentRequestData);
    },
    async sendRequest() {
      this.showResponse = false;
      this.isLoading = true;

      try {
        // 模拟API请求延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        this.responseData = {
          status: 200,
          statusText: 'OK',
          time: 245,
          size: '1.2KB',
          headers: {
            'content-type': 'application/json',
            'content-length': '1200'
          },
          body: {
            success: true,
            message: 'Login successful',
            data: {
              user: {
                id: 12345,
                username: 'zhang',
                email: '<EMAIL>'
              },
              token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
            }
          }
        };
        this.showResponse = true;
      } catch (error) {
        this.$message.error('Request failed: ' + error.message);
      } finally {
        this.isLoading = false;
      }
    },
    saveRequest() {
      console.log('保存请求:', this.currentRequestData);
      // 这里应该调用保存API
    },

    getMethodTagType(method) {
      const types = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'info'
      };
      return types[method] || 'success';
    },
    handleTabClick(tab) {
      // 如果点击的是新建标签页或环境标签页，不切换请求
      if (tab.name === 'add-new' || tab.name === 'environment') {
        return;
      }
      this.switchRequest(tab.name);
    },
    handleEnvironmentCommand(command) {
      if (command === 'manage') {
        this.$emit('show-environment-modal');
      } else if (command === 'select') {
        // 模拟选择环境
        this.selectedEnvironment = 'Development';
      }
    },
    handleSendCommand(command) {
      if (command === 'send') {
        this.sendRequest();
      } else if (command === 'send-and-save') {
        this.sendRequest();
        this.saveRequest();
      }
    },
    handleSaveCommand(command) {
      if (command === 'save') {
        this.saveRequest();
      } else if (command === 'save-as') {
        // 处理另存为逻辑
        console.log('另存为');
      }
    }
  },
  components: {
    ParametersTab,
    BodyTab,
    HeadersTab,
    AuthorizationTab,
    PreScriptTab,
    PostScriptTab,
    ResponseArea
  }
}
</script>

<style scoped>
.api-case-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  overflow: hidden;
  position: relative;
  padding: 4px 8px 0 4px;
  /* border-bottom: 1px solid #e0e0e0;
  box-sizing: border-box;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04); */
}

/* 标签页基础样式优化 */
.api-case-content>>>.el-tabs__header {
  margin: 0;
  background: transparent;
}

.api-case-content>>>.el-tabs__nav-wrap {
  /* margin-bottom: 0; */
  position: relative;
}

.api-case-content>>>.el-tabs__nav {
  display: flex;
  align-items: center;
}

.api-case-content>>>.el-tabs__item {
  height: 36px;
  line-height: 36px;
  padding: 0 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid transparent;
  border-radius: 6px 6px 0 0;
  margin-right: 4px;
  transition: all 0.2s ease;
  color: #606266;
  background: #f8f9fa;
}

.api-case-content>>>.el-tabs__item:hover {
  background: rgba(24, 144, 255, 0.08);
  color: #1890ff;
  border-color: rgba(24, 144, 255, 0.2);
}

.api-case-content>>>.el-tabs__item.is-active {
  background: white;
  color: #1890ff;
  border-color: #e0e0e0;
  border-bottom-color: white;
  font-weight: 600;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
}

.api-case-content>>>.el-tabs__content {
  padding: 0;
  height: calc(100% - 34px);
  overflow: hidden;
  /* background: #f5f5f5; */
}

.api-case-content>>>.el-tabs__active-bar {
  display: none;
}

/* 方法标签优化 */
.method-tag {
  font-size: 8px;
  padding: 2px 6px;
  margin-right: 8px;
  height: 18px;
  line-height: 14px;
  font-weight: 700;
  border-radius: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 标签页内容区域优化 */
.tab-pane-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  overflow: hidden;
  /* border-radius: 0 8px 8px 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); */
}

/* 请求构建区域优化 */
.request-builder {
  flex-shrink: 0;
  padding: 16px 20px;
  /* background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%); */
  /* border-bottom: 1px solid #e8ecf0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04); */
}

.request-header {
  display: flex;
  gap: 12px;
  align-items: center;
}

.method-select {
  width: 110px;
  flex-shrink: 0;
}

.method-select>>>.el-input__inner {
  font-weight: 600;
  text-align: center;
  border-radius: 6px;
  border: 2px solid #e0e6ed;
  transition: all 0.2s ease;
}

.method-select>>>.el-input__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.url-input {
  flex: 1;
  min-width: 200px;
}

.url-input>>>.el-input__inner {
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  border-radius: 6px;
  border: 2px solid #e0e6ed;
  transition: all 0.2s ease;
  background: white;
}

.url-input>>>.el-input__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.action-buttons-right {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 请求内容区域优化 */
.request-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-tabs>>>.el-tabs__header {
  flex-shrink: 0;
  margin: 0;
  background: #f8f9fa;
  border-bottom: 1px solid #e8ecf0;
  padding: 0 8px;
}

.content-tabs>>>.el-tabs__nav-wrap {
  /* margin-bottom: 0; */
}

.content-tabs>>>.el-tabs__item {
  height: 40px;
  line-height: 40px;
  padding: 0 24px;
  font-size: 13px;
  font-weight: 500;
  color: #606266;
  border-radius: 6px 6px 0 0;
  margin-right: 2px;
  transition: all 0.2s ease;
}

.content-tabs>>>.el-tabs__item:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.04);
}

.content-tabs>>>.el-tabs__item.is-active {
  color: #1890ff;
  background: white;
  font-weight: 600;
  border: 1px solid #e8ecf0;
  border-bottom: 1px solid white;
  /* margin-bottom: -1px; */
}

.content-tabs>>>.el-tabs__content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  min-height: 0;
  background: white;
}

.content-tabs>>>.el-tabs__active-bar {
  display: none;
}

/* 环境选择器优化 */
.environment-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
}

.env-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border: 2px solid #e0e6ed;
  border-radius: 6px;
  cursor: pointer;
  background: white;
  font-size: 12px;
  height: 32px;
  transition: all 0.2s ease;
  min-width: 160px;
  font-weight: 500;
}

.env-selector:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.env-view-btn {
  width: 28px !important;
  height: 28px !important;
  min-width: 28px !important;
  padding: 0 !important;
  border-radius: 6px !important;
  border: 2px solid #e0e6ed !important;
  transition: all 0.2s ease !important;
}

.env-view-btn:hover {
  border-color: #1890ff !important;
  color: #1890ff !important;
  transform: translateY(-1px) !important;
}

/* 新建标签页优化 */
.add-new-tab {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  cursor: pointer;
  padding: 0 12px;
  transition: all 0.2s ease;
  height: 100%;
  border-radius: 6px;
  border: 2px dashed #d0d7de;
  background: #f8f9fa;
}

.add-new-tab:hover {
  color: #1890ff;
  border-color: #1890ff;
  background: rgba(24, 144, 255, 0.04);
  transform: translateY(-1px);
}

.add-new-tab i {
  font-size: 14px;
  font-weight: bold;
}

/* 特殊标签页布局优化 */
.api-case-content>>>.el-tabs__item[aria-controls*="environment"] {
  margin-left: auto;
  min-width: 240px;
  padding: 0;
  order: 999;
  background: transparent;
  border: none;
}

.api-case-content>>>.el-tabs__item[aria-controls*="environment"]:hover {
  background: transparent;
}

.api-case-content>>>.el-tabs__item[aria-controls*="add-new"] {
  width: 48px;
  min-width: 48px;
  padding: 4px;
  text-align: center;
  order: 998;
  background: transparent;
  border: none;
}

.api-case-content>>>.el-tabs__item[aria-controls*="add-new"]:hover {
  background: transparent;
}

.api-case-content>>>.el-tabs__item:not([aria-controls*="environment"]):not([aria-controls*="add-new"]) {
  order: 1;
}

/* 隐藏特殊标签页内容 */
.api-case-content>>>.el-tab-pane[id*="environment"],
.api-case-content>>>.el-tab-pane[id*="add-new"] {
  display: none;
}

/* 全局组件样式优化 */
.api-case-content>>>.el-button--mini {
  padding: 8px 16px;
  font-size: 12px;
  height: 32px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  border-width: 2px;
}

.api-case-content>>>.el-input--mini .el-input__inner {
  height: 32px;
  line-height: 32px;
  font-size: 12px;
  border-radius: 6px;
  border-width: 2px;
  transition: all 0.2s ease;
}

.api-case-content>>>.el-select--mini .el-input__inner {
  height: 32px;
  line-height: 32px;
  font-size: 12px;
  font-weight: 600;
  border-width: 2px;
}

/* 主题色彩优化 */
.api-case-content>>>.el-button--primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transform: translateY(0);
  transition: all 0.2s ease;
}

.api-case-content>>>.el-button--primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  border-color: #40a9ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.api-case-content>>>.el-button--primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .request-header {
    flex-wrap: wrap;
    gap: 8px;
  }

  .method-select {
    width: 100px;
  }

  .url-input {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .api-case-content {
    padding: 4px 8px 0 8px;
    max-height: 80px;
  }

  .request-builder {
    padding: 12px 16px;
  }

  .request-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .method-select,
  .url-input {
    width: 100%;
  }

  .action-buttons-right {
    width: 100%;
    justify-content: space-between;
  }

  .api-case-content>>>.el-tabs__content {
    height: calc(100% - 80px);
  }

  .api-case-content>>>.el-tabs__item[aria-controls*="environment"] {
    margin-left: 0;
    min-width: auto;
    order: unset;
    width: 100%;
  }

  .api-case-content>>>.el-tabs__item[aria-controls*="add-new"] {
    order: unset;
    width: auto;
    min-width: auto;
  }

  .environment-tab {
    justify-content: center;
  }

  .env-selector {
    min-width: auto;
    flex: 1;
  }

  /* 移动端按钮优化 */
  .api-case-content>>>.el-button--mini {
    padding: 6px 12px;
    height: 28px;
    font-size: 11px;
  }

  .api-case-content>>>.el-input--mini .el-input__inner {
    height: 28px;
    line-height: 28px;
    font-size: 11px;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
/* .api-case-content>>>.el-button.is-loading {
  pointer-events: none;
  opacity: 0.8;
} */

/* 滚动条优化 */
/* .content-tabs>>>.el-tabs__content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.content-tabs>>>.el-tabs__content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.content-tabs>>>.el-tabs__content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.content-tabs>>>.el-tabs__content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
} */

/* 焦点状态优化 */
/* .api-case-content>>>.el-input__inner:focus,
.api-case-content>>>.el-textarea__inner:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
} */
</style>
