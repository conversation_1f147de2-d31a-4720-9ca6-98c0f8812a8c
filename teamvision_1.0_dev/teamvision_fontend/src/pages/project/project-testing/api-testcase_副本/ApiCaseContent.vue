<template>
  <div class="api-case-content">
    <!-- 请求标签页栏 -->
    <div class="request-tabs-bar">
      <div class="request-tabs">
        <div 
          v-for="request in requestTabs" 
          :key="request.id"
          :class="['request-tab', { active: activeRequestId === request.id }]"
          @click="switchRequest(request.id)"
        >
          <span class="tab-method" :style="{ backgroundColor: getMethodColor(request.method) }">
            {{ request.method }}
          </span>
          <span class="tab-name">{{ request.name }}</span>
          <span class="tab-close" @click.stop="closeRequestTab(request.id)">×</span>
        </div>
        <div class="request-tab new-tab" @click="createNewRequest">
          <span>+</span>
        </div>
      </div>
      <div class="tab-actions">
        <div class="env-selector" @click="$emit('show-environment-modal')">
          <span>📋</span>
          <span>Select environment</span>
          <span>▼</span>
        </div>
        <button class="eye-btn" title="View environment details">
          <span>👁</span>
        </button>
        <div class="variables-section">
          <span>Variables</span>
          <button class="add-var-btn" @click="addVariable">
            <span>+</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 请求构建区域 -->
    <div class="request-builder">
      <div class="request-header">
        <select v-model="currentRequestData.method" class="method-select">
          <option value="GET">GET</option>
          <option value="POST">POST</option>
          <option value="PUT">PUT</option>
          <option value="DELETE">DELETE</option>
          <option value="PATCH">PATCH</option>
        </select>
        <input 
          v-model="currentRequestData.url" 
          type="text" 
          class="url-input" 
          placeholder="Enter request URL"
          @input="updateRequest"
        >
        <div class="action-buttons-right">
          <button class="send-btn" @click="sendRequest">
            <span>▶</span> Send
            <span class="dropdown-arrow">▼</span>
          </button>
          <button class="save-btn" @click="saveRequest">
            <span>💾</span> Save
            <span class="dropdown-arrow">▼</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 选项卡 -->
    <div class="tabs">
      <div 
        v-for="tab in tabs" 
        :key="tab.key"
        :class="['tab', { active: activeTab === tab.key }]"
        @click="switchTab(tab.key)"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div class="main-content">
        <!-- Parameters 内容 -->
        <parameters-tab v-if="activeTab === 'parameters'" />
        
        <!-- Body 内容 -->
        <body-tab v-if="activeTab === 'body'" />
        
        <!-- Headers 内容 -->
        <headers-tab v-if="activeTab === 'headers'" />
        
        <!-- Authorization 内容 -->
        <authorization-tab v-if="activeTab === 'authorization'" />
        
        <!-- Pre-request Script 内容 -->
        <pre-script-tab v-if="activeTab === 'pre-script'" />
        
        <!-- Post-request Script 内容 -->
        <post-script-tab v-if="activeTab === 'post-script'" />
      </div>
    </div>

    <!-- 响应区域 -->
    <response-area 
      v-if="showResponse"
      :response="responseData"
    />
  </div>
</template>

<script>
import ParametersTab from './components/tabs/ParametersTab.vue';
import BodyTab from './components/tabs/BodyTab.vue';
import HeadersTab from './components/tabs/HeadersTab.vue';
import AuthorizationTab from './components/tabs/AuthorizationTab.vue';
import PreScriptTab from './components/tabs/PreScriptTab.vue';
import PostScriptTab from './components/tabs/PostScriptTab.vue';
import ResponseArea from './components/ResponseArea.vue';

export default {
  name: 'ApiCaseContent',
  props: {
    projectID: {
      type: String,
      required: true
    },
    currentRequest: {
      type: Object,
      default: () => ({
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login',
        name: 'login'
      })
    }
  },
  data() {
    return {
      activeRequestId: 'login',
      activeTab: 'parameters',
      showResponse: false,
      requestTabs: [
        {
          id: 'login',
          method: 'GET',
          name: 'login',
          url: 'https://echo.hoppscotch.io/login'
        }
      ],
      currentRequestData: {
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login',
        name: 'login'
      },
      tabs: [
        { key: 'parameters', label: 'Parameters' },
        { key: 'body', label: 'Body' },
        { key: 'headers', label: 'Headers' },
        { key: 'authorization', label: 'Authorization' },
        { key: 'pre-script', label: 'Pre-request Script' },
        { key: 'post-script', label: 'Post-request Script' }
      ],
      responseData: null
    }
  },
  methods: {
    switchRequest(requestId) {
      this.activeRequestId = requestId;
      const request = this.requestTabs.find(r => r.id === requestId);
      if (request) {
        this.currentRequestData = { ...request };
        this.$emit('update-request', this.currentRequestData);
      }
    },
    createNewRequest() {
      const newId = 'request_' + Date.now();
      const newRequest = {
        id: newId,
        method: 'GET',
        name: 'New Request',
        url: ''
      };
      this.requestTabs.push(newRequest);
      this.switchRequest(newId);
    },
    closeRequestTab(requestId) {
      const index = this.requestTabs.findIndex(r => r.id === requestId);
      if (index > -1) {
        this.requestTabs.splice(index, 1);
        if (this.activeRequestId === requestId && this.requestTabs.length > 0) {
          this.switchRequest(this.requestTabs[this.requestTabs.length - 1].id);
        }
      }
    },
    switchTab(tabKey) {
      this.activeTab = tabKey;
    },
    updateRequest() {
      const request = this.requestTabs.find(r => r.id === this.activeRequestId);
      if (request) {
        request.method = this.currentRequestData.method;
        request.url = this.currentRequestData.url;
        request.name = this.currentRequestData.name;
      }
      this.$emit('update-request', this.currentRequestData);
    },
    sendRequest() {
      // 模拟发送请求
      this.showResponse = false;
      
      // 显示加载状态
      setTimeout(() => {
        this.responseData = {
          status: 200,
          statusText: 'OK',
          time: 245,
          size: '1.2KB',
          headers: {
            'content-type': 'application/json',
            'content-length': '1200'
          },
          body: {
            success: true,
            message: 'Login successful',
            data: {
              user: {
                id: 12345,
                username: 'zhang',
                email: '<EMAIL>'
              },
              token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
            }
          }
        };
        this.showResponse = true;
      }, 1500);
    },
    saveRequest() {
      console.log('保存请求:', this.currentRequestData);
      // 这里应该调用保存API
    },
    addVariable() {
      const name = prompt('请输入变量名:');
      if (name && name.trim()) {
        const value = prompt('请输入变量值:');
        if (value !== null) {
          console.log('添加变量:', name, value);
          // 这里应该调用添加变量的逻辑
        }
      }
    },
    getMethodColor(method) {
      const colors = {
        'GET': '#52c41a',
        'POST': '#1890ff',
        'PUT': '#fa8c16',
        'DELETE': '#ff4d4f',
        'PATCH': '#722ed1'
      };
      return colors[method] || '#52c41a';
    }
  },
  components: {
    ParametersTab,
    BodyTab,
    HeadersTab,
    AuthorizationTab,
    PreScriptTab,
    PostScriptTab,
    ResponseArea
  }
}
</script>

<style scoped>
.api-case-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 请求标签页栏 */
.request-tabs-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
  height: 48px;
}

.request-tabs {
  display: flex;
  align-items: center;
  gap: 4px;
}

.request-tab {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  background: #f5f5f5;
  border: 1px solid transparent;
  border-bottom: none;
  position: relative;
  min-width: 120px;
}

.request-tab.active {
  background: white;
  border-color: #e0e0e0;
  color: #722ed1;
  font-weight: 600;
}

.request-tab:hover:not(.active) {
  background: #fafafa;
}

.tab-method {
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  margin-right: 8px;
}

.tab-name {
  flex: 1;
  font-size: 14px;
}

.tab-close {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
  opacity: 0;
  transition: opacity 0.2s;
}

.request-tab:hover .tab-close {
  opacity: 1;
}

.tab-close:hover {
  background: #ff4d4f;
  color: white;
}

.new-tab {
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  color: #666;
  font-size: 18px;
  font-weight: bold;
}

.new-tab:hover {
  background: #e0e0e0;
  color: #333;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.env-selector {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  background: white;
  font-size: 14px;
}

.env-selector:hover {
  border-color: #1890ff;
}

.eye-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.eye-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.variables-section {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.add-var-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
}

.add-var-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

/* 请求构建区域 */
.request-builder {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.request-header {
  display: flex;
  gap: 12px;
  align-items: center;
}

.method-select {
  background: #52c41a;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
}

.url-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.action-buttons-right {
  display: flex;
  gap: 8px;
}

.send-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.save-btn {
  background: white;
  color: #666;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.dropdown-arrow {
  margin-left: 4px;
  font-size: 10px;
  opacity: 0.7;
}

/* 选项卡 */
.tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background: white;
}

.tab {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  color: #666;
}

.tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.tab:hover {
  color: #1890ff;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  background: #fafafa;
}

.main-content {
  flex: 1;
  padding: 20px;
  background: white;
  margin: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
</style>
