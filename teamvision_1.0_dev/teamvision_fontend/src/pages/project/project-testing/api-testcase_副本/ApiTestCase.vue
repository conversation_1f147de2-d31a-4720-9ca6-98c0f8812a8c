<template>
  <div class="api-test-case-container" :style="{ height: containerHeight + 'px' }">
    <!-- 左侧导航栏 -->
    <div class="nav-sidebar">
      <div 
        v-for="nav in navItems" 
        :key="nav.key"
        :class="['nav-icon', { active: activeNavTab === nav.key }]"
        :title="nav.title"
        @click="switchNavTab(nav.key)"
      >
        <span>{{ nav.icon }}</span>
      </div>
    </div>

    <!-- 左侧功能面板 -->
    <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
      <!-- Collections 选项卡 -->
      <collections-panel 
        v-if="activeNavTab === 'collections'"
        :projectID="projectID"
      />
      
      <!-- Environments 选项卡 -->
      <environments-panel 
        v-if="activeNavTab === 'environments'"
        :projectID="projectID"
        @show-environment-modal="showEnvironmentModal"
      />
      
      <!-- History 选项卡 -->
      <history-panel 
        v-if="activeNavTab === 'history'"
        :projectID="projectID"
      />
      
      <!-- Code snippet 选项卡 -->
      <code-snippet-panel 
        v-if="activeNavTab === 'code'"
        :current-request="currentRequest"
      />
    </div>

    <!-- 右侧主工作区 -->
    <div class="right-panel">
      <api-case-content 
        :projectID="projectID"
        :current-request="currentRequest"
        @update-request="updateCurrentRequest"
      />
    </div>

    <!-- 环境管理弹窗 -->
    <environment-modal
      v-if="showEnvModal"
      :visible="showEnvModal"
      @close="hideEnvironmentModal"
      @save="saveEnvironment"
    />
  </div>
</template>

<script>
import ApiCaseContent from "./ApiCaseContent.vue";
import CollectionsPanel from "./components/CollectionsPanel.vue";
import EnvironmentsPanel from "./components/EnvironmentsPanel.vue";
import HistoryPanel from "./components/HistoryPanel.vue";
import CodeSnippetPanel from "./components/CodeSnippetPanel.vue";
import EnvironmentModal from "./components/EnvironmentModal.vue";
import { mapState } from "vuex";

export default {
  name: "ApiTestCase",
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      activeNavTab: 'collections',
      leftPanelWidth: 360,
      showEnvModal: false,
      currentRequest: {
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login',
        name: 'login'
      },
      navItems: [
        { key: 'collections', title: 'Collections', icon: '📁' },
        { key: 'environments', title: 'Environments', icon: '📋' },
        { key: 'history', title: 'History', icon: '🕒' },
        { key: 'code', title: 'Code', icon: '</>' }
      ]
    }
  },
  computed: {
    ...mapState(['appBodyHeight']),
    containerHeight() {
      return this.appBodyHeight
    }
  },
  methods: {
    switchNavTab(tabKey) {
      this.activeNavTab = tabKey;
    },
    showEnvironmentModal() {
      this.showEnvModal = true;
    },
    hideEnvironmentModal() {
      this.showEnvModal = false;
    },
    saveEnvironment(environmentData) {
      console.log('保存环境:', environmentData);
      this.hideEnvironmentModal();
    },
    updateCurrentRequest(requestData) {
      this.currentRequest = { ...this.currentRequest, ...requestData };
    }
  },
  created() {
    // 初始化逻辑
  },
  components: {
    ApiCaseContent,
    CollectionsPanel,
    EnvironmentsPanel,
    HistoryPanel,
    CodeSnippetPanel,
    EnvironmentModal
  }
}
</script>

<style scoped>
.api-test-case-container {
  display: flex;
  height: 100vh;
  background: white;
}

/* 左侧导航栏 */
.nav-sidebar {
  width: 60px;
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.nav-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  color: #666;
  font-size: 18px;
  transition: all 0.2s ease;
}

.nav-icon:hover {
  background: #e9ecef;
  color: #333;
}

.nav-icon.active {
  background: #1890ff;
  color: white;
}

/* 左侧功能面板 */
.left-panel {
  background: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

/* 右侧主工作区 */
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .api-test-case-container {
    flex-direction: column;
  }
  
  .nav-sidebar {
    width: 100%;
    height: 60px;
    flex-direction: row;
    padding: 10px 20px;
  }
  
  .nav-icon {
    margin-bottom: 0;
    margin-right: 8px;
  }
  
  .left-panel {
    width: 100% !important;
    height: 200px;
  }
}
</style>
