<template>
  <div class="post-script-tab">
    <div class="section-title">
      Post-request Script
    </div>
    
    <div class="script-info">
      <p class="info-text">
        This script will run after the request is sent. You can use it to test the response and set variables.
      </p>
    </div>
    
    <div class="script-editor">
      <div class="editor-header">
        <div class="editor-actions">
          <button class="action-btn" title="Format code" @click="formatCode">
            <span>📋</span>
          </button>
          <button class="action-btn" title="Clear" @click="clearScript">
            <span>🗑</span>
          </button>
        </div>
        <div class="language-badge">JavaScript</div>
      </div>
      
      <textarea 
        v-model="scriptContent"
        class="script-textarea"
        placeholder="// Write your post-request script here&#10;// Example:&#10;// pm.test('Status code is 200', function () {&#10;//     pm.response.to.have.status(200);&#10;// });&#10;//&#10;// pm.test('Response has user data', function () {&#10;//     const jsonData = pm.response.json();&#10;//     pm.expect(jsonData).to.have.property('user');&#10;// });"
        @input="updateScript"
      ></textarea>
    </div>
    
    <div class="script-examples">
      <h4>Common Examples:</h4>
      <div class="example-item" @click="insertExample('status')">
        Test status code
      </div>
      <div class="example-item" @click="insertExample('json')">
        Test JSON response
      </div>
      <div class="example-item" @click="insertExample('extract')">
        Extract data to variable
      </div>
      <div class="example-item" @click="insertExample('time')">
        Test response time
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PostScriptTab',
  data() {
    return {
      scriptContent: '',
      examples: {
        status: '// Test status code\npm.test("Status code is 200", function () {\n    pm.response.to.have.status(200);\n});',
        json: '// Test JSON response\npm.test("Response has expected data", function () {\n    const jsonData = pm.response.json();\n    pm.expect(jsonData).to.have.property("success");\n    pm.expect(jsonData.success).to.be.true;\n});',
        extract: '// Extract data to variable\npm.test("Extract token", function () {\n    const jsonData = pm.response.json();\n    pm.environment.set("authToken", jsonData.token);\n});',
        time: '// Test response time\npm.test("Response time is less than 1000ms", function () {\n    pm.expect(pm.response.responseTime).to.be.below(1000);\n});'
      }
    }
  },
  methods: {
    updateScript() {
      this.$emit('update-post-script', this.scriptContent);
    },
    formatCode() {
      console.log('格式化代码');
    },
    clearScript() {
      if (confirm('确定要清空脚本吗？')) {
        this.scriptContent = '';
        this.updateScript();
      }
    },
    insertExample(type) {
      const example = this.examples[type];
      if (example) {
        this.scriptContent += (this.scriptContent ? '\n\n' : '') + example;
        this.updateScript();
      }
    }
  }
}
</script>

<style scoped>
.post-script-tab {
  width: 100%;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.script-info {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.info-text {
  font-size: 13px;
  color: #52c41a;
  margin: 0;
}

.script-editor {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.editor-header {
  background: #fafafa;
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.action-btn:hover {
  background: #f0f0f0;
}

.language-badge {
  background: #1890ff;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
}

.script-textarea {
  width: 100%;
  height: 200px;
  padding: 12px;
  border: none;
  outline: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  background: #f8f9fa;
}

.script-examples {
  margin-top: 16px;
}

.script-examples h4 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.example-item {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  transition: all 0.2s ease;
}

.example-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}
</style>
