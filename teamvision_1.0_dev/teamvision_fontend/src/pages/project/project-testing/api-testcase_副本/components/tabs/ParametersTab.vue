<template>
  <div class="parameters-tab">
    <div class="section-title">
      Query Parameters
      <div class="section-actions">
        <button class="action-icon" title="Help">?</button>
        <button class="action-icon" title="Delete all" @click="clearAll">🗑</button>
        <button class="action-icon" title="Edit">✏</button>
        <button class="action-icon" title="Add" @click="addParameter">+</button>
      </div>
    </div>
    
    <table class="params-table">
      <thead>
        <tr>
          <th style="width: 40px;"></th>
          <th>Key</th>
          <th>Value</th>
          <th>Description</th>
          <th style="width: 80px;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(param, index) in parameters" :key="index">
          <td>
            <input 
              v-model="param.enabled"
              type="checkbox" 
              class="checkbox"
            >
          </td>
          <td>
            <input 
              v-model="param.key"
              type="text" 
              placeholder="Parameter name"
              @input="updateParameter"
            >
          </td>
          <td>
            <input 
              v-model="param.value"
              type="text" 
              placeholder="Parameter value"
              @input="updateParameter"
            >
          </td>
          <td>
            <input 
              v-model="param.description"
              type="text" 
              placeholder="Description"
              @input="updateParameter"
            >
          </td>
          <td>
            <div class="param-actions">
              <button 
                class="param-action success" 
                :title="param.enabled ? 'Disable' : 'Enable'"
                @click="toggleParameter(index)"
              >
                ✓
              </button>
              <button 
                class="param-action danger" 
                title="Delete"
                @click="removeParameter(index)"
              >
                🗑
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    
    <button class="add-param-btn" @click="addParameter">
      <span>+</span> Add Parameter
    </button>
  </div>
</template>

<script>
export default {
  name: 'ParametersTab',
  data() {
    return {
      parameters: [
        {
          key: '',
          value: '',
          description: '',
          enabled: true
        }
      ]
    }
  },
  methods: {
    addParameter() {
      this.parameters.push({
        key: '',
        value: '',
        description: '',
        enabled: true
      });
    },
    removeParameter(index) {
      this.parameters.splice(index, 1);
    },
    toggleParameter(index) {
      this.parameters[index].enabled = !this.parameters[index].enabled;
    },
    updateParameter() {
      // 可以在这里触发父组件的更新事件
      this.$emit('update-parameters', this.parameters);
    },
    clearAll() {
      if (confirm('确定要清空所有参数吗？')) {
        this.parameters = [];
      }
    }
  }
}
</script>

<style scoped>
.parameters-tab {
  width: 100%;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon:hover {
  background: #f0f0f0;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.params-table th {
  background: #fafafa;
  padding: 12px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.params-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.params-table input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  padding: 4px;
}

.params-table input:focus {
  background: #f8f9fa;
}

.checkbox {
  width: 16px;
  height: 16px;
}

.param-actions {
  display: flex;
  gap: 4px;
}

.param-action {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.param-action:hover {
  background: #f0f0f0;
}

.param-action.success {
  color: #52c41a;
}

.param-action.success:hover {
  background: #f6ffed;
}

.param-action.danger {
  color: #ff4d4f;
}

.param-action.danger:hover {
  background: #fff2f0;
}

.add-param-btn {
  width: 100%;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  background: white;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.add-param-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}
</style>
