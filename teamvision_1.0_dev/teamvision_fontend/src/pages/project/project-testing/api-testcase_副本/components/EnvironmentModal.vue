<template>
  <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal" @click.stop>
      <div class="modal-header">
        <div class="modal-title">New Environment</div>
        <button class="modal-close" @click="$emit('close')">×</button>
      </div>
      
      <div class="modal-body">
        <div class="form-group">
          <label class="form-label">Label</label>
          <input 
            v-model="environmentData.label"
            type="text" 
            class="form-input" 
            placeholder="Environment name (e.g., dev, test, prod)"
          >
        </div>
        
        <div class="modal-tabs">
          <div 
            :class="['modal-tab', { active: activeTab === 'variables' }]"
            @click="activeTab = 'variables'"
          >
            Variables
          </div>
          <div 
            :class="['modal-tab', { active: activeTab === 'secrets' }]"
            @click="activeTab = 'secrets'"
          >
            Secrets
          </div>
        </div>

        <!-- Variables 页签 -->
        <div v-if="activeTab === 'variables'" class="modal-tab-content">
          <div class="section-title">
            Variables
            <div class="section-actions">
              <button class="action-icon" title="Add" @click="addVariable">
                <span>+</span>
              </button>
              <button class="action-icon" title="Paste" @click="pasteVariables">
                <span>📋</span>
              </button>
            </div>
          </div>
          <table class="params-table">
            <thead>
              <tr>
                <th>Key</th>
                <th>Value</th>
                <th>Description</th>
                <th style="width: 60px;">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(variable, index) in environmentData.variables" :key="index">
                <td>
                  <input 
                    v-model="variable.key"
                    type="text" 
                    placeholder="Variable name"
                  >
                </td>
                <td>
                  <input 
                    v-model="variable.value"
                    type="text" 
                    placeholder="Variable value"
                  >
                </td>
                <td>
                  <input 
                    v-model="variable.description"
                    type="text" 
                    placeholder="Description"
                  >
                </td>
                <td>
                  <div class="param-actions">
                    <button class="param-action" title="Copy" @click="copyVariable(variable)">
                      <span>📋</span>
                    </button>
                    <button class="param-action" title="Delete" @click="removeVariable(index)">
                      <span>🗑</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <button class="add-param-btn" @click="addVariable">
            <span>+</span> Add Variable
          </button>
        </div>

        <!-- Secrets 页签 -->
        <div v-if="activeTab === 'secrets'" class="modal-tab-content">
          <div class="section-title">
            Secrets
            <div class="section-actions">
              <button class="action-icon" title="Add" @click="addSecret">
                <span>+</span>
              </button>
              <button class="action-icon" title="Paste" @click="pasteSecrets">
                <span>📋</span>
              </button>
            </div>
          </div>
          <table class="params-table">
            <thead>
              <tr>
                <th>Key</th>
                <th>Value</th>
                <th>Description</th>
                <th style="width: 60px;">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(secret, index) in environmentData.secrets" :key="index">
                <td>
                  <input 
                    v-model="secret.key"
                    type="text" 
                    placeholder="Secret name"
                  >
                </td>
                <td>
                  <input 
                    v-model="secret.value"
                    type="password" 
                    placeholder="Secret value"
                  >
                </td>
                <td>
                  <input 
                    v-model="secret.description"
                    type="text" 
                    placeholder="Description"
                  >
                </td>
                <td>
                  <div class="param-actions">
                    <button class="param-action" title="Copy" @click="copySecret(secret)">
                      <span>📋</span>
                    </button>
                    <button class="param-action" title="Delete" @click="removeSecret(index)">
                      <span>🗑</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <button class="add-param-btn" @click="addSecret">
            <span>+</span> Add Secret
          </button>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="$emit('close')">Cancel</button>
        <button class="btn btn-primary" @click="saveEnvironment">Save</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnvironmentModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: 'variables',
      environmentData: {
        label: '',
        variables: [
          {
            key: 'user',
            value: 'zhang',
            description: ''
          },
          {
            key: 'password',
            value: '123456',
            description: ''
          }
        ],
        secrets: [
          {
            key: 'apiKey',
            value: '••••••••••••',
            description: ''
          }
        ]
      }
    }
  },
  methods: {
    handleOverlayClick() {
      this.$emit('close');
    },
    addVariable() {
      this.environmentData.variables.push({
        key: '',
        value: '',
        description: ''
      });
    },
    removeVariable(index) {
      this.environmentData.variables.splice(index, 1);
    },
    addSecret() {
      this.environmentData.secrets.push({
        key: '',
        value: '',
        description: ''
      });
    },
    removeSecret(index) {
      this.environmentData.secrets.splice(index, 1);
    },
    copyVariable(variable) {
      navigator.clipboard.writeText(`${variable.key}=${variable.value}`);
    },
    copySecret(secret) {
      navigator.clipboard.writeText(`${secret.key}=${secret.value}`);
    },
    pasteVariables() {
      navigator.clipboard.readText().then(text => {
        const lines = text.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            this.environmentData.variables.push({
              key: key.trim(),
              value: value.trim(),
              description: ''
            });
          }
        });
      });
    },
    pasteSecrets() {
      navigator.clipboard.readText().then(text => {
        const lines = text.split('\n');
        lines.forEach(line => {
          const [key, value] = line.split('=');
          if (key && value) {
            this.environmentData.secrets.push({
              key: key.trim(),
              value: value.trim(),
              description: ''
            });
          }
        });
      });
    },
    saveEnvironment() {
      if (!this.environmentData.label.trim()) {
        alert('请输入环境名称');
        return;
      }
      
      // 验证变量key是否重复
      const variableKeys = this.environmentData.variables.map(v => v.key).filter(k => k);
      const secretKeys = this.environmentData.secrets.map(s => s.key).filter(k => k);
      const allKeys = [...variableKeys, ...secretKeys];
      
      if (new Set(allKeys).size !== allKeys.length) {
        alert('变量名不能重复');
        return;
      }
      
      this.$emit('save', this.environmentData);
    }
  }
}
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: white;
  border-radius: 8px;
  width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0,0,0,0.2);
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 18px;
}

.modal-close:hover {
  background: #f0f0f0;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.modal-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 16px;
}

.modal-tab {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  color: #666;
}

.modal-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.modal-tab:hover {
  color: #1890ff;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon:hover {
  background: #f0f0f0;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.params-table th {
  background: #fafafa;
  padding: 12px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.params-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.params-table input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  padding: 4px;
}

.params-table input:focus {
  background: #f8f9fa;
}

.param-actions {
  display: flex;
  gap: 4px;
}

.param-action {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
}

.param-action:hover {
  background: #f0f0f0;
}

.add-param-btn {
  width: 100%;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  background: white;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.add-param-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid transparent;
}

.btn-primary {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.btn-secondary {
  background: white;
  color: #666;
  border-color: #ddd;
}

.btn:hover {
  opacity: 0.8;
}
</style>
