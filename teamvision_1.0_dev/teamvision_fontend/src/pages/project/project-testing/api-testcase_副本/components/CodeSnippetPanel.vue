<template>
  <div class="code-snippet-panel">
    <div class="left-header">
      <div class="breadcrumb">
        Request > Code snippet
      </div>
      <div class="language-selector">
        <select v-model="selectedLanguage" @change="generateCodeSnippet">
          <option value="shell-curl">Shell - cURL</option>
          <option value="javascript-fetch">JavaScript - Fetch</option>
          <option value="python-requests">Python - Requests</option>
          <option value="php-curl">PHP - cURL</option>
          <option value="go-http">Go - HTTP</option>
          <option value="java-http">Java - HTTP</option>
        </select>
      </div>
    </div>
    
    <div class="code-section">
      <div class="section-title">
        Generated code
        <div class="section-actions">
          <button class="action-icon" title="Format" @click="formatCode">
            <span>📋</span>
          </button>
          <button class="action-icon" title="Download" @click="downloadCode">
            <span>↓</span>
          </button>
          <button class="action-icon" title="Copy" @click="copyCode">
            <span>📄</span>
          </button>
        </div>
      </div>
      <div class="code-editor">
        <div 
          v-for="(line, index) in codeLines" 
          :key="index"
          class="code-line"
        >
          <span class="line-number">{{ index + 1 }}</span>
          <span class="code-content" v-html="highlightSyntax(line)"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CodeSnippetPanel',
  props: {
    currentRequest: {
      type: Object,
      default: () => ({
        method: 'GET',
        url: 'https://echo.hoppscotch.io/login'
      })
    }
  },
  data() {
    return {
      selectedLanguage: 'shell-curl',
      generatedCode: ''
    }
  },
  computed: {
    codeLines() {
      return this.generatedCode.split('\n');
    }
  },
  watch: {
    currentRequest: {
      handler() {
        this.generateCodeSnippet();
      },
      deep: true
    }
  },
  mounted() {
    this.generateCodeSnippet();
  },
  methods: {
    generateCodeSnippet() {
      const { method, url } = this.currentRequest;
      
      switch (this.selectedLanguage) {
        case 'shell-curl':
          this.generatedCode = `curl --request ${method} \\\n--url ${url}`;
          break;
        case 'javascript-fetch':
          this.generatedCode = `fetch('${url}', {\n  method: '${method}',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n.then(response => response.json())\n.then(data => console.log(data))\n.catch(error => console.error('Error:', error));`;
          break;
        case 'python-requests':
          this.generatedCode = `import requests\n\nresponse = requests.${method.toLowerCase()}('${url}')\nprint(response.json())`;
          break;
        case 'php-curl':
          this.generatedCode = `<?php\n$ch = curl_init();\ncurl_setopt($ch, CURLOPT_URL, '${url}');\ncurl_setopt($ch, CURLOPT_RETURNTRANSFER, true);\ncurl_setopt($ch, CURLOPT_CUSTOMREQUEST, '${method}');\n$response = curl_exec($ch);\ncurl_close($ch);\necho $response;`;
          break;
        case 'go-http':
          this.generatedCode = `package main\n\nimport (\n    "fmt"\n    "io/ioutil"\n    "net/http"\n)\n\nfunc main() {\n    resp, err := http.Get("${url}")\n    if err != nil {\n        panic(err)\n    }\n    defer resp.Body.Close()\n    body, err := ioutil.ReadAll(resp.Body)\n    fmt.Println(string(body))\n}`;
          break;
        case 'java-http':
          this.generatedCode = `import java.net.http.*;\nimport java.net.URI;\n\nHttpClient client = HttpClient.newHttpClient();\nHttpRequest request = HttpRequest.newBuilder()\n    .uri(URI.create("${url}"))\n    .method("${method}", HttpRequest.BodyPublishers.noBody())\n    .build();\nHttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());\nSystem.out.println(response.body());`;
          break;
        default:
          this.generatedCode = `# ${this.selectedLanguage} code for ${method} ${url}`;
      }
    },
    highlightSyntax(line) {
      // 简单的语法高亮
      return line
        .replace(/curl|fetch|requests|HttpClient/g, '<span class="keyword">$&</span>')
        .replace(/GET|POST|PUT|DELETE|PATCH/g, '<span class="method">$&</span>')
        .replace(/--request|--url|method|headers|Content-Type/g, '<span class="flag">$&</span>')
        .replace(/https?:\/\/[^\s'"]+/g, '<span class="url">$&</span>')
        .replace(/".*?"|'.*?'/g, '<span class="string">$&</span>');
    },
    formatCode() {
      // 格式化代码
      console.log('格式化代码');
    },
    downloadCode() {
      const blob = new Blob([this.generatedCode], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `request_${this.currentRequest.method.toLowerCase()}_${Date.now()}.${this.getFileExtension()}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    },
    copyCode() {
      navigator.clipboard.writeText(this.generatedCode).then(() => {
        // 可以添加复制成功的提示
        console.log('代码已复制到剪贴板');
      }).catch(err => {
        console.error('复制失败:', err);
      });
    },
    getFileExtension() {
      const extensions = {
        'shell-curl': 'sh',
        'javascript-fetch': 'js',
        'python-requests': 'py',
        'php-curl': 'php',
        'go-http': 'go',
        'java-http': 'java'
      };
      return extensions[this.selectedLanguage] || 'txt';
    }
  }
}
</script>

<style scoped>
.code-snippet-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.language-selector {
  margin-bottom: 16px;
}

.language-selector select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.code-section {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.action-icon:hover {
  background: #f0f0f0;
}

.code-editor {
  flex: 1;
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow: hidden;
}

.code-line {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.code-line:last-child {
  border-bottom: none;
}

.line-number {
  width: 30px;
  color: #999;
  font-size: 12px;
  text-align: right;
  margin-right: 12px;
  user-select: none;
  flex-shrink: 0;
}

.code-content {
  flex: 1;
  white-space: pre-wrap;
  word-break: break-all;
}

/* 语法高亮样式 */
.keyword {
  color: #d73a49;
  font-weight: bold;
}

.method {
  color: #6f42c1;
  font-weight: bold;
}

.flag {
  color: #005cc5;
}

.url {
  color: #032f62;
}

.string {
  color: #032f62;
}
</style>
