<template>
  <div class="environments-panel">
    <div class="left-header">
      <div class="breadcrumb">
        Personal Workspace > Environments
      </div>
      <div class="environment-header">
        <span class="globe-icon">🌐</span>
        <span class="header-title">Global</span>
        <div class="tree-actions">
          <span>⋯</span>
        </div>
      </div>
      <input 
        v-model="searchQuery" 
        type="text" 
        class="search-bar" 
        placeholder="Search"
      >
      <div class="action-buttons">
        <button class="new-btn" @click="$emit('show-environment-modal')">
          <span>+</span> New
        </button>
        <div class="header-icons">
          <button class="icon-btn" title="Help">
            <span>?</span>
          </button>
          <button class="icon-btn" title="Folders">
            <span>📁</span>
          </button>
        </div>
      </div>
    </div>
    
    <div class="empty-state" v-if="environments.length === 0">
      <div class="empty-icon">
        <div class="cube-icon">🎯</div>
      </div>
      <div class="empty-title">Environments are empty</div>
      <div class="empty-subtitle">Import or create an environment</div>
      <div class="empty-actions">
        <button class="import-btn" @click="importEnvironment">
          <span>📥</span> Import
        </button>
        <button class="add-btn" @click="$emit('show-environment-modal')">
          <span>+</span> Add new
        </button>
      </div>
    </div>

    <div v-else class="environments-list">
      <div 
        v-for="env in filteredEnvironments" 
        :key="env.id"
        class="environment-item"
        @click="selectEnvironment(env)"
      >
        <div class="env-info">
          <div class="env-name">{{ env.name }}</div>
          <div class="env-description">{{ env.description }}</div>
        </div>
        <div class="env-actions">
          <button class="env-action-btn" @click.stop="editEnvironment(env)">
            <span>✏</span>
          </button>
          <button class="env-action-btn" @click.stop="deleteEnvironment(env.id)">
            <span>🗑</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnvironmentsPanel',
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      searchQuery: '',
      environments: [] // 初始为空数组，显示空状态
    }
  },
  computed: {
    filteredEnvironments() {
      if (!this.searchQuery) {
        return this.environments;
      }
      return this.environments.filter(env => 
        env.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  methods: {
    selectEnvironment(env) {
      console.log('选择环境:', env);
      // 这里应该触发环境选择事件
    },
    editEnvironment(env) {
      console.log('编辑环境:', env);
      // 这里应该触发编辑环境事件
    },
    deleteEnvironment(envId) {
      if (confirm('确定要删除这个环境吗？')) {
        const index = this.environments.findIndex(env => env.id === envId);
        if (index > -1) {
          this.environments.splice(index, 1);
        }
      }
    },
    importEnvironment() {
      // 创建文件输入元素
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            try {
              const data = JSON.parse(e.target.result);
              console.log('导入环境数据:', data);
              alert('环境导入成功！');
              // 这里应该将导入的数据添加到environments数组中
            } catch (error) {
              alert('文件格式错误，请选择有效的JSON文件');
            }
          };
          reader.readAsText(file);
        }
      };
      input.click();
    }
  }
}
</script>

<style scoped>
.environments-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.environment-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.globe-icon {
  margin-right: 8px;
  font-size: 16px;
}

.header-title {
  flex: 1;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.tree-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.environment-header:hover .tree-actions {
  opacity: 1;
}

.search-bar {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.new-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-icons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.icon-btn:hover {
  background: #f0f0f0;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 20px;
}

.cube-icon {
  font-size: 64px;
  opacity: 0.6;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 24px;
}

.empty-actions {
  display: flex;
  gap: 12px;
}

.import-btn {
  background: #722ed1;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-btn {
  background: white;
  color: #666;
  border: 1px solid #ddd;
  padding: 12px 20px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.environments-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.environment-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
}

.environment-item:hover {
  background: #f5f5f5;
  border-color: #e0e0e0;
}

.env-info {
  flex: 1;
}

.env-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.env-description {
  font-size: 12px;
  color: #666;
}

.env-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.environment-item:hover .env-actions {
  opacity: 1;
}

.env-action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 12px;
}

.env-action-btn:hover {
  background: #f0f0f0;
}
</style>
