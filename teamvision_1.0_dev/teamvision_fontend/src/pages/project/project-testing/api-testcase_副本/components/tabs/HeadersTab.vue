<template>
  <div class="headers-tab">
    <div class="section-title">
      Request Headers
      <div class="section-actions">
        <button class="action-icon" title="Help">?</button>
        <button class="action-icon" title="Delete all" @click="clearAll">🗑</button>
        <button class="action-icon" title="Add" @click="addHeader">+</button>
      </div>
    </div>
    
    <table class="params-table">
      <thead>
        <tr>
          <th style="width: 40px;"></th>
          <th>Key</th>
          <th>Value</th>
          <th>Description</th>
          <th style="width: 60px;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(header, index) in headers" :key="index">
          <td>
            <input 
              v-model="header.enabled"
              type="checkbox" 
              class="checkbox"
            >
          </td>
          <td>
            <input 
              v-model="header.key"
              type="text" 
              placeholder="Header name"
              @input="updateHeaders"
            >
          </td>
          <td>
            <input 
              v-model="header.value"
              type="text" 
              placeholder="Header value"
              @input="updateHeaders"
            >
          </td>
          <td>
            <input 
              v-model="header.description"
              type="text" 
              placeholder="Description"
            >
          </td>
          <td>
            <div class="param-actions">
              <button 
                class="param-action success" 
                title="Copy"
                @click="copyHeader(header)"
              >
                ✓
              </button>
              <button 
                class="param-action danger" 
                title="Delete"
                @click="removeHeader(index)"
              >
                🗑
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    
    <button class="add-param-btn" @click="addHeader">
      <span>+</span> Add Header
    </button>
  </div>
</template>

<script>
export default {
  name: 'HeadersTab',
  data() {
    return {
      headers: [
        {
          key: 'Content-Type',
          value: 'application/json',
          description: '',
          enabled: true
        }
      ]
    }
  },
  methods: {
    addHeader() {
      this.headers.push({
        key: '',
        value: '',
        description: '',
        enabled: true
      });
    },
    removeHeader(index) {
      this.headers.splice(index, 1);
      this.updateHeaders();
    },
    copyHeader(header) {
      navigator.clipboard.writeText(`${header.key}: ${header.value}`);
    },
    updateHeaders() {
      this.$emit('update-headers', this.headers);
    },
    clearAll() {
      if (confirm('确定要清空所有头部吗？')) {
        this.headers = [];
        this.updateHeaders();
      }
    }
  }
}
</script>

<style scoped>
.headers-tab {
  width: 100%;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon:hover {
  background: #f0f0f0;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.params-table th {
  background: #fafafa;
  padding: 12px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.params-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.params-table input {
  width: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  padding: 4px;
}

.params-table input:focus {
  background: #f8f9fa;
}

.checkbox {
  width: 16px;
  height: 16px;
}

.param-actions {
  display: flex;
  gap: 4px;
}

.param-action {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.param-action:hover {
  background: #f0f0f0;
}

.param-action.success {
  color: #52c41a;
}

.param-action.success:hover {
  background: #f6ffed;
}

.param-action.danger {
  color: #ff4d4f;
}

.param-action.danger:hover {
  background: #fff2f0;
}

.add-param-btn {
  width: 100%;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  background: white;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.add-param-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}
</style>
