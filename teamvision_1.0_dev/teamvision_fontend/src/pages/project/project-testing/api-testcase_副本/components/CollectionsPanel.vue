<template>
  <div class="collections-panel">
    <div class="left-header">
      <div class="breadcrumb">
        Personal Workspace > Collections
      </div>
      <input 
        v-model="searchQuery" 
        type="text" 
        class="search-bar" 
        placeholder="Search"
      >
      <div class="action-buttons">
        <button class="new-btn" @click="showNewCollectionDialog">
          <span>+</span> New
        </button>
        <div class="header-icons">
          <button class="icon-btn" title="Help">
            <span>?</span>
          </button>
          <button class="icon-btn" title="Download">
            <span>↓</span>
          </button>
          <button class="icon-btn" title="More">
            <span>⋯</span>
          </button>
        </div>
      </div>
    </div>
    
    <div class="collection-tree">
      <div 
        v-for="item in filteredItems" 
        :key="item.id"
        :class="['tree-item', item.type]"
        @click="selectItem(item)"
      >
        <span class="tree-icon">{{ item.icon }}</span>
        <span v-if="item.method" class="method-badge" :style="{ backgroundColor: getMethodColor(item.method) }">
          {{ item.method }}
        </span>
        <span class="tree-label">{{ item.name }}</span>
        <span v-if="item.status" class="status-dot" :class="item.status"></span>
        <div class="tree-actions">
          <span>⋯</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollectionsPanel',
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      searchQuery: '',
      collections: [
        {
          id: 'login',
          type: 'folder',
          name: 'login',
          icon: '📁'
        },
        {
          id: 'login-request',
          type: 'request',
          name: 'login',
          method: 'GET',
          status: 'success',
          parentId: 'login'
        }
      ]
    }
  },
  computed: {
    filteredItems() {
      if (!this.searchQuery) {
        return this.collections;
      }
      return this.collections.filter(item => 
        item.name.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  methods: {
    selectItem(item) {
      console.log('选择项目:', item);
      // 这里应该触发选择事件
    },
    showNewCollectionDialog() {
      const name = prompt('请输入集合名称:');
      if (name && name.trim()) {
        const newCollection = {
          id: 'collection_' + Date.now(),
          type: 'folder',
          name: name.trim(),
          icon: '📁'
        };
        this.collections.push(newCollection);
        console.log('创建集合:', newCollection);
      }
    },
    getMethodColor(method) {
      const colors = {
        'GET': '#52c41a',
        'POST': '#1890ff',
        'PUT': '#fa8c16',
        'DELETE': '#ff4d4f',
        'PATCH': '#722ed1'
      };
      return colors[method] || '#52c41a';
    }
  }
}
</script>

<style scoped>
.collections-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.search-bar {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.new-btn {
  background: #1890ff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-icons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.icon-btn:hover {
  background: #f0f0f0;
}

.collection-tree {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.tree-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
}

.tree-item:hover {
  background: #f5f5f5;
}

.tree-item.folder {
  margin-left: 0;
}

.tree-item.request {
  margin-left: 24px;
}

.tree-icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.tree-label {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.method-badge {
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
  margin-right: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-dot.success {
  background: #52c41a;
}

.tree-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-item:hover .tree-actions {
  opacity: 1;
}
</style>
