<template>
  <div class="response-area">
    <div class="response-header">
      <span class="status-code" :class="getStatusClass(response.status)">
        {{ response.status }} {{ response.statusText }}
      </span>
      <span class="response-meta">
        Time: {{ response.time }}ms | Size: {{ response.size }}
      </span>
    </div>
    
    <div class="response-tabs">
      <div 
        v-for="tab in responseTabs" 
        :key="tab.key"
        :class="['response-tab', { active: activeResponseTab === tab.key }]"
        @click="activeResponseTab = tab.key"
      >
        {{ tab.label }}
      </div>
    </div>
    
    <div class="response-content">
      <!-- Headers Tab -->
      <div v-if="activeResponseTab === 'headers'" class="headers-content">
        <div class="headers-list">
          <div 
            v-for="(value, key) in response.headers" 
            :key="key"
            class="header-item"
          >
            <div class="header-key">{{ key }}:</div>
            <div class="header-value">{{ value }}</div>
          </div>
        </div>
      </div>
      
      <!-- Body Tab -->
      <div v-if="activeResponseTab === 'body'" class="body-content">
        <div class="body-actions">
          <button class="action-btn" title="Copy response" @click="copyResponse">
            <span>📄</span> Copy
          </button>
          <button class="action-btn" title="Download response" @click="downloadResponse">
            <span>↓</span> Download
          </button>
        </div>
        <div class="response-body">
          <pre><code v-html="formatJson(response.body)"></code></pre>
        </div>
      </div>
      
      <!-- Tests Tab -->
      <div v-if="activeResponseTab === 'tests'" class="tests-content">
        <div class="test-results">
          <div class="test-summary">
            <div class="test-stat">
              <span class="stat-value passed">{{ testResults.passed }}</span>
              <span class="stat-label">Passed</span>
            </div>
            <div class="test-stat">
              <span class="stat-value failed">{{ testResults.failed }}</span>
              <span class="stat-label">Failed</span>
            </div>
            <div class="test-stat">
              <span class="stat-value total">{{ testResults.total }}</span>
              <span class="stat-label">Total</span>
            </div>
          </div>
          
          <div class="test-list">
            <div 
              v-for="(test, index) in testResults.tests" 
              :key="index"
              :class="['test-item', test.status]"
            >
              <div class="test-icon">
                <span v-if="test.status === 'passed'">✓</span>
                <span v-else>✗</span>
              </div>
              <div class="test-details">
                <div class="test-name">{{ test.name }}</div>
                <div v-if="test.error" class="test-error">{{ test.error }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResponseArea',
  props: {
    response: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeResponseTab: 'body',
      responseTabs: [
        { key: 'headers', label: 'Headers' },
        { key: 'body', label: 'Body' },
        { key: 'tests', label: 'Tests' }
      ],
      testResults: {
        passed: 2,
        failed: 0,
        total: 2,
        tests: [
          {
            name: 'Status code is 200',
            status: 'passed'
          },
          {
            name: 'Response has user data',
            status: 'passed'
          }
        ]
      }
    }
  },
  methods: {
    getStatusClass(status) {
      if (status >= 200 && status < 300) {
        return 'status-2xx';
      } else if (status >= 300 && status < 400) {
        return 'status-3xx';
      } else if (status >= 400 && status < 500) {
        return 'status-4xx';
      } else if (status >= 500) {
        return 'status-5xx';
      }
      return '';
    },
    formatJson(obj) {
      if (typeof obj === 'string') {
        try {
          obj = JSON.parse(obj);
        } catch (e) {
          return obj;
        }
      }
      return JSON.stringify(obj, null, 2);
    },
    copyResponse() {
      navigator.clipboard.writeText(this.formatJson(this.response.body));
    },
    downloadResponse() {
      const blob = new Blob([this.formatJson(this.response.body)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `response_${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }
  }
}
</script>

<style scoped>
.response-area {
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  overflow: hidden;
}

.response-header {
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-code {
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.status-2xx {
  background: #52c41a;
}

.status-3xx {
  background: #fa8c16;
}

.status-4xx {
  background: #ff4d4f;
}

.status-5xx {
  background: #722ed1;
}

.response-meta {
  font-size: 12px;
  color: #666;
}

.response-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background: #fafafa;
}

.response-tab {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 14px;
  color: #666;
}

.response-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  background: white;
}

.response-tab:hover {
  color: #1890ff;
}

.response-content {
  min-height: 200px;
}

.headers-content {
  padding: 16px;
}

.headers-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.header-item {
  display: flex;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.header-item:last-child {
  border-bottom: none;
}

.header-key {
  font-weight: 600;
  color: #333;
  min-width: 150px;
  margin-right: 12px;
}

.header-value {
  color: #666;
  flex: 1;
}

.body-content {
  display: flex;
  flex-direction: column;
}

.body-actions {
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  color: #666;
}

.action-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.response-body {
  flex: 1;
  padding: 16px;
  background: #f8f9fa;
  overflow: auto;
  max-height: 300px;
}

.response-body pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

.tests-content {
  padding: 16px;
}

.test-summary {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.test-stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-value.passed {
  color: #52c41a;
}

.stat-value.failed {
  color: #ff4d4f;
}

.stat-value.total {
  color: #1890ff;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.test-list {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.test-item:last-child {
  border-bottom: none;
}

.test-item.passed {
  background: #f6ffed;
}

.test-item.failed {
  background: #fff2f0;
}

.test-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  margin-right: 12px;
}

.test-item.passed .test-icon {
  background: #52c41a;
  color: white;
}

.test-item.failed .test-icon {
  background: #ff4d4f;
  color: white;
}

.test-details {
  flex: 1;
}

.test-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.test-error {
  font-size: 12px;
  color: #ff4d4f;
}
</style>
