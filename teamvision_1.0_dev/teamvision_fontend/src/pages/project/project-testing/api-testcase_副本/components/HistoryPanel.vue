<template>
  <div class="history-panel">
    <div class="left-header">
      <div class="breadcrumb">
        Personal Workspace > History
      </div>
      <input 
        v-model="searchQuery" 
        type="text" 
        class="search-bar" 
        placeholder="Search history"
      >
      <div class="action-buttons">
        <button class="clear-btn" @click="clearHistory">
          <span>🗑</span> Clear
        </button>
        <div class="header-icons">
          <button class="icon-btn" title="Help">
            <span>?</span>
          </button>
          <button class="icon-btn" title="Export">
            <span>📤</span>
          </button>
        </div>
      </div>
    </div>
    
    <div class="history-list">
      <div 
        v-for="item in filteredHistory" 
        :key="item.id"
        class="history-item"
        @click="selectHistoryItem(item)"
      >
        <div class="history-info">
          <div class="request-method" :style="{ backgroundColor: getMethodColor(item.method) }">
            {{ item.method }}
          </div>
          <div class="request-details">
            <div class="request-name">{{ item.name }}</div>
            <div class="request-url">{{ item.url }}</div>
            <div class="request-time">{{ formatTime(item.timestamp) }}</div>
          </div>
        </div>
        <div class="history-status">
          <div class="status-badge" :class="item.status">
            {{ item.status === 'success' ? '✓' : '✗' }}
          </div>
        </div>
      </div>
    </div>

    <div v-if="historyItems.length === 0" class="empty-history">
      <div class="empty-icon">🕒</div>
      <div class="empty-text">No history yet</div>
      <div class="empty-subtext">Start making requests to see them here</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HistoryPanel',
  props: {
    projectID: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      searchQuery: '',
      historyItems: [
        {
          id: '1',
          method: 'GET',
          name: 'login',
          url: 'https://echo.hoppscotch.io/login',
          status: 'success',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
          duration: 245,
          size: '1.2KB'
        },
        {
          id: '2',
          method: 'POST',
          name: 'create user',
          url: 'https://echo.hoppscotch.io/users',
          status: 'success',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
          duration: 180,
          size: '800B'
        },
        {
          id: '3',
          method: 'GET',
          name: 'get profile',
          url: 'https://echo.hoppscotch.io/profile',
          status: 'error',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
          duration: 5000,
          size: '0B'
        }
      ]
    }
  },
  computed: {
    filteredHistory() {
      if (!this.searchQuery) {
        return this.historyItems;
      }
      return this.historyItems.filter(item => 
        item.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        item.url.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  methods: {
    selectHistoryItem(item) {
      console.log('选择历史记录:', item);
      // 这里应该触发选择历史记录事件，可能重新发送请求
    },
    clearHistory() {
      if (confirm('确定要清除所有历史记录吗？')) {
        this.historyItems = [];
      }
    },
    formatTime(timestamp) {
      const now = new Date();
      const diff = now - timestamp;
      
      if (diff < 1000 * 60) {
        return '刚刚';
      } else if (diff < 1000 * 60 * 60) {
        return `${Math.floor(diff / (1000 * 60))}分钟前`;
      } else if (diff < 1000 * 60 * 60 * 24) {
        return `${Math.floor(diff / (1000 * 60 * 60))}小时前`;
      } else {
        return timestamp.toLocaleDateString();
      }
    },
    getMethodColor(method) {
      const colors = {
        'GET': '#52c41a',
        'POST': '#1890ff',
        'PUT': '#fa8c16',
        'DELETE': '#ff4d4f',
        'PATCH': '#722ed1'
      };
      return colors[method] || '#52c41a';
    }
  }
}
</script>

<style scoped>
.history-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.left-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.breadcrumb {
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.search-bar {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
}

.clear-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-icons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

.icon-btn:hover {
  background: #f0f0f0;
}

.history-list {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.history-item:hover {
  background: #f5f5f5;
  border-color: #e0e0e0;
}

.history-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.request-method {
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
  margin-right: 12px;
  min-width: 50px;
  text-align: center;
}

.request-details {
  flex: 1;
}

.request-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 2px;
}

.request-url {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.request-time {
  font-size: 11px;
  color: #999;
}

.history-status {
  margin-left: 12px;
}

.status-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.success {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-badge.error {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.empty-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.6;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #666;
}
</style>
