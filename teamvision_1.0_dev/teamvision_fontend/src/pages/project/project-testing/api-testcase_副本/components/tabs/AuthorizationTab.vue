<template>
  <div class="authorization-tab">
    <div class="section-title">
      Authorization
    </div>
    
    <div class="form-group">
      <label class="form-label">Type</label>
      <select v-model="authType" class="form-input" @change="updateAuth">
        <option value="no-auth">No Auth</option>
        <option value="basic">Basic Auth</option>
        <option value="bearer">Bearer <PERSON>ken</option>
        <option value="api-key">API Key</option>
        <option value="oauth2">OAuth 2.0</option>
      </select>
    </div>
    
    <!-- Basic Auth -->
    <div v-if="authType === 'basic'" class="auth-section">
      <div class="form-group">
        <label class="form-label">Username</label>
        <input 
          v-model="authData.username"
          type="text" 
          class="form-input" 
          placeholder="Enter username"
          @input="updateAuth"
        >
      </div>
      <div class="form-group">
        <label class="form-label">Password</label>
        <input 
          v-model="authData.password"
          type="password" 
          class="form-input" 
          placeholder="Enter password"
          @input="updateAuth"
        >
      </div>
    </div>
    
    <!-- Bearer Token -->
    <div v-if="authType === 'bearer'" class="auth-section">
      <div class="form-group">
        <label class="form-label">Token</label>
        <input 
          v-model="authData.token"
          type="password" 
          class="form-input" 
          placeholder="Enter bearer token"
          @input="updateAuth"
        >
      </div>
    </div>
    
    <!-- API Key -->
    <div v-if="authType === 'api-key'" class="auth-section">
      <div class="form-group">
        <label class="form-label">Key</label>
        <input 
          v-model="authData.key"
          type="text" 
          class="form-input" 
          placeholder="Enter API key name"
          @input="updateAuth"
        >
      </div>
      <div class="form-group">
        <label class="form-label">Value</label>
        <input 
          v-model="authData.value"
          type="password" 
          class="form-input" 
          placeholder="Enter API key value"
          @input="updateAuth"
        >
      </div>
      <div class="form-group">
        <label class="form-label">Add to</label>
        <select v-model="authData.addTo" class="form-input">
          <option value="header">Header</option>
          <option value="query">Query Params</option>
        </select>
      </div>
    </div>
    
    <!-- OAuth 2.0 -->
    <div v-if="authType === 'oauth2'" class="auth-section">
      <div class="form-group">
        <label class="form-label">Grant Type</label>
        <select v-model="authData.grantType" class="form-input">
          <option value="authorization-code">Authorization Code</option>
          <option value="client-credentials">Client Credentials</option>
          <option value="password">Password</option>
          <option value="refresh-token">Refresh Token</option>
        </select>
      </div>
      <div class="form-group">
        <label class="form-label">Access Token</label>
        <input 
          v-model="authData.accessToken"
          type="password" 
          class="form-input" 
          placeholder="Enter access token"
          @input="updateAuth"
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AuthorizationTab',
  data() {
    return {
      authType: 'no-auth',
      authData: {
        username: '',
        password: '',
        token: '',
        key: '',
        value: '',
        addTo: 'header',
        grantType: 'authorization-code',
        accessToken: ''
      }
    }
  },
  methods: {
    updateAuth() {
      this.$emit('update-auth', {
        type: this.authType,
        data: this.authData
      });
    }
  }
}
</script>

<style scoped>
.authorization-tab {
  width: 100%;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
}

.auth-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;
}
</style>
