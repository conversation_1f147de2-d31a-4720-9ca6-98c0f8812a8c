<template>
  <div class="body-tab">
    <div class="section-title">
      Request Body
      <div class="section-actions">
        <select v-model="bodyType" class="body-type-select">
          <option value="form-data">form-data</option>
          <option value="x-www-form-urlencoded">x-www-form-urlencoded</option>
          <option value="raw">raw</option>
          <option value="binary">binary</option>
        </select>
      </div>
    </div>
    
    <div v-if="bodyType === 'raw'" class="raw-body-section">
      <div class="content-type-selector">
        <label>Content-Type:</label>
        <select v-model="contentType">
          <option value="application/json">application/json</option>
          <option value="application/xml">application/xml</option>
          <option value="text/plain">text/plain</option>
          <option value="text/html">text/html</option>
        </select>
      </div>
      <textarea 
        v-model="rawBody"
        class="body-textarea"
        placeholder="Enter request body (JSON, XML, etc.)"
        @input="updateBody"
      ></textarea>
    </div>
    
    <div v-else-if="bodyType === 'form-data'" class="form-data-section">
      <table class="params-table">
        <thead>
          <tr>
            <th style="width: 40px;"></th>
            <th>Key</th>
            <th>Value</th>
            <th>Type</th>
            <th style="width: 80px;">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(field, index) in formDataFields" :key="index">
            <td>
              <input 
                v-model="field.enabled"
                type="checkbox" 
                class="checkbox"
              >
            </td>
            <td>
              <input 
                v-model="field.key"
                type="text" 
                placeholder="Field name"
              >
            </td>
            <td>
              <input 
                v-model="field.value"
                type="text" 
                placeholder="Field value"
              >
            </td>
            <td>
              <select v-model="field.type">
                <option value="text">Text</option>
                <option value="file">File</option>
              </select>
            </td>
            <td>
              <div class="param-actions">
                <button 
                  class="param-action success" 
                  title="Enable/Disable"
                  @click="toggleField(index)"
                >
                  ✓
                </button>
                <button 
                  class="param-action danger" 
                  title="Delete"
                  @click="removeField(index)"
                >
                  🗑
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <button class="add-param-btn" @click="addField">
        <span>+</span> Add Field
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BodyTab',
  data() {
    return {
      bodyType: 'raw',
      contentType: 'application/json',
      rawBody: '',
      formDataFields: []
    }
  },
  methods: {
    addField() {
      this.formDataFields.push({
        key: '',
        value: '',
        type: 'text',
        enabled: true
      });
    },
    removeField(index) {
      this.formDataFields.splice(index, 1);
    },
    toggleField(index) {
      this.formDataFields[index].enabled = !this.formDataFields[index].enabled;
    },
    updateBody() {
      this.$emit('update-body', {
        type: this.bodyType,
        content: this.bodyType === 'raw' ? this.rawBody : this.formDataFields,
        contentType: this.contentType
      });
    }
  },
  watch: {
    bodyType() {
      this.updateBody();
    }
  }
}
</script>

<style scoped>
.body-tab {
  width: 100%;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.body-type-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 14px;
}

.raw-body-section {
  display: flex;
  flex-direction: column;
}

.content-type-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.content-type-selector label {
  font-size: 14px;
  color: #666;
}

.content-type-selector select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-size: 14px;
}

.body-textarea {
  width: 100%;
  height: 200px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.params-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.params-table th {
  background: #fafafa;
  padding: 12px;
  text-align: left;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  border-bottom: 1px solid #e0e0e0;
}

.params-table td {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.params-table input,
.params-table select {
  width: 100%;
  border: none;
  outline: none;
  font-size: 14px;
  padding: 4px;
}

.params-table input:focus {
  background: #f8f9fa;
}

.checkbox {
  width: 16px;
  height: 16px;
}

.param-actions {
  display: flex;
  gap: 4px;
}

.param-action {
  width: 20px;
  height: 20px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.param-action:hover {
  background: #f0f0f0;
}

.param-action.success {
  color: #52c41a;
}

.param-action.success:hover {
  background: #f6ffed;
}

.param-action.danger {
  color: #ff4d4f;
}

.param-action.danger:hover {
  background: #fff2f0;
}

.add-param-btn {
  width: 100%;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  background: white;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.add-param-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}
</style>
