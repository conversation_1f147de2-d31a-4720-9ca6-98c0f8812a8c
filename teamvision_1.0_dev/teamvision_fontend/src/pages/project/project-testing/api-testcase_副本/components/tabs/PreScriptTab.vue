<template>
  <div class="pre-script-tab">
    <div class="section-title">
      Pre-request Script
    </div>
    
    <div class="script-info">
      <p class="info-text">
        This script will run before the request is sent. You can use it to modify the request or set variables.
      </p>
    </div>
    
    <div class="script-editor">
      <div class="editor-header">
        <div class="editor-actions">
          <button class="action-btn" title="Format code" @click="formatCode">
            <span>📋</span>
          </button>
          <button class="action-btn" title="Clear" @click="clearScript">
            <span>🗑</span>
          </button>
        </div>
        <div class="language-badge">JavaScript</div>
      </div>
      
      <textarea 
        v-model="scriptContent"
        class="script-textarea"
        placeholder="// Write your pre-request script here&#10;// Example:&#10;// pm.environment.set('timestamp', Date.now());&#10;// pm.globals.set('randomId', Math.random().toString(36));"
        @input="updateScript"
      ></textarea>
    </div>
    
    <div class="script-examples">
      <h4>Common Examples:</h4>
      <div class="example-item" @click="insertExample('timestamp')">
        Set timestamp variable
      </div>
      <div class="example-item" @click="insertExample('random')">
        Generate random ID
      </div>
      <div class="example-item" @click="insertExample('auth')">
        Set authorization header
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PreScriptTab',
  data() {
    return {
      scriptContent: '',
      examples: {
        timestamp: '// Set current timestamp\npm.environment.set("timestamp", Date.now());',
        random: '// Generate random ID\npm.environment.set("randomId", Math.random().toString(36).substring(2, 15));',
        auth: '// Set authorization header\npm.request.headers.add({\n  key: "Authorization",\n  value: "Bearer " + pm.environment.get("token")\n});'
      }
    }
  },
  methods: {
    updateScript() {
      this.$emit('update-pre-script', this.scriptContent);
    },
    formatCode() {
      // 简单的代码格式化（实际项目中可以使用更专业的格式化工具）
      console.log('格式化代码');
    },
    clearScript() {
      if (confirm('确定要清空脚本吗？')) {
        this.scriptContent = '';
        this.updateScript();
      }
    },
    insertExample(type) {
      const example = this.examples[type];
      if (example) {
        this.scriptContent += (this.scriptContent ? '\n\n' : '') + example;
        this.updateScript();
      }
    }
  }
}
</script>

<style scoped>
.pre-script-tab {
  width: 100%;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.script-info {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

.info-text {
  font-size: 13px;
  color: #52c41a;
  margin: 0;
}

.script-editor {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
}

.editor-header {
  background: #fafafa;
  padding: 8px 12px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #666;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.action-btn:hover {
  background: #f0f0f0;
}

.language-badge {
  background: #1890ff;
  color: white;
  padding: 2px 8px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: bold;
}

.script-textarea {
  width: 100%;
  height: 200px;
  padding: 12px;
  border: none;
  outline: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  background: #f8f9fa;
}

.script-examples {
  margin-top: 16px;
}

.script-examples h4 {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.example-item {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 4px;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  transition: all 0.2s ease;
}

.example-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}
</style>
