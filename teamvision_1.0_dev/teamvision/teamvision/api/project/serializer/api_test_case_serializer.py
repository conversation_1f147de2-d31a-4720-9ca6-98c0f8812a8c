# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""

from rest_framework import serializers
from business.project.scenes_service import ScenesService
from business.project.tag_service import TagService
from teamvision.project import models
from business.auth_user.user_service import UserService
from concurrent.futures import ThreadPoolExecutor, wait, FIRST_COMPLETED


class ProjectTestCaseSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        # result["child_case_count"] = self.child_case_count(obj)
        result["child_case_count"] = 0
        result["priority"], result["tag_color"] = self.priority(obj)
        result["Scenes"] = self.scenes(obj)
        return result

    def child_case_count(self, obj):
        child_case = models.ProjectTestCase.objects.get_descendant_case_count(obj.Project, obj.id)
        return child_case

    def priority(self, obj):
        name, color = TagService.get_tag_name(6, obj.Priority)
        return name, color

    def scenes(self, obj):
        result = ScenesService.get_scenes_name(obj.Scenes)
        return result

    class Meta:
        model = models.ProjectTestCase
        exclude = ('IsActive', 'CreationTime')
        read_only_fields = ('id', 'UpdateTime',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseTreeCaseSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result["priority"], result["tag_color"] = self.priority(obj)
        return result

    def priority(self, obj):
        name, color = TagService.get_tag_name(6, obj.Priority)
        return name, color

    class Meta:
        model = models.ProjectTestCase
        exclude = (
            'CreationTime', 'IsActive', 'Desc', 'ExpectResult', 'Precondition', 'Module', 'RunTimes', 'UpdateTime',
            'Scenes', 'automate', 'automatedCompletion')
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseTreeSerializer(serializers.ModelSerializer):
    # CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    # UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    children = serializers.SerializerMethodField()
    view_data = serializers.SerializerMethodField()

    #
    def get_view_data(self, obj):
        result = dict()
        result["child_case_count"] = self.child_case_count(obj)
        return result

    def child_case_count(self, obj):
        # child_case_count = models.ProjectTestCase.objects.all().filter(Parent=obj.id).filter(IsGroup=False).count()
        child_case_count = 0
        return child_case_count

    def get_children(self, obj):
        result = list()
        child_nodes = models.ProjectTestCase.objects.get_children(obj.id).filter(IsActive=1).order_by("IsGroup")

        if str(obj.group) == "1":
            child_nodes = child_nodes.filter(IsGroup=True)
        if len(child_nodes) > 0:
            """
            for child_node in child_nodes:
            self.get_child_data(child_node,result,obj.group)
            """
            with ThreadPoolExecutor(max_workers=10) as t:
                all_task = []
                for child_node in child_nodes:
                    task = t.submit(self.get_child_data, child_node, result, obj.group)
                    all_task.append(task)
                wait(all_task, return_when=FIRST_COMPLETED)
        return result

    def get_child_data(self, parent_node, child_data, is_group):
        if parent_node:
            temp_serializer = ProjectTestCaseTreeCaseSerializer(instance=parent_node)
            temp_data = temp_serializer.data

            if parent_node.IsGroup == False:
                child_data.append(temp_data)
                return
            temp_data["children"] = list()
            child_nodes = models.ProjectTestCase.objects.get_children(parent_node.id).filter(IsActive=1).order_by(
                "IsGroup")
            if str(is_group) == "1":
                child_nodes = child_nodes.filter(IsGroup=True)
            if len(child_nodes) > 0:
                for child_node in child_nodes:
                    self.get_child_data(child_node, temp_data["children"], is_group)
                child_data.append(temp_data)
            else:
                child_data.append(temp_data)
                return
        else:
            return

    class Meta:
        model = models.ProjectTestCase
        exclude = ('IsActive', 'Desc', 'ExpectResult', 'Precondition', 'Module', 'RunTimes', 'Scenes', 'automate',
                   'automatedCompletion', 'CreationTime', 'UpdateTime')
        read_only_fields = ('id',)


class ProjectTestCaseDirSerializer(serializers.ModelSerializer):
    class Meta:
        model = models.ProjectTestCase
        exclude = ('CreationTime', 'IsActive', 'Desc', 'ExpectResult', 'Precondition', 'Priority', 'Module', 'RunTimes',
                   'UpdateTime', 'Project', 'Scenes', 'automate', 'accessTest', 'automatedCompletion')
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseTreeLazySerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        # result["child_case_count"] = models.ProjectTestCase.objects.get_descendant_case_count(obj.id)
        result["child_case_count"] = 0
        return result

    class Meta:
        model = models.ProjectTestCase
        exclude = ('CreationTime', 'IsActive', 'Desc', 'ExpectResult', 'Precondition', 'Priority', 'Module', 'RunTimes',
                   'UpdateTime', 'Project', 'Scenes', 'automate', 'accessTest', 'automatedCompletion')
        read_only_fields = ('id',)


class ProjectAllTestCaseTreeSerializer(serializers.ModelSerializer):
    children = serializers.SerializerMethodField()
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result["child_case_count"] = 0
        return result

    def get_children(self, obj):
        result = list()
        child_nodes = models.ProjectTestCase.objects.get_children(obj.id).filter(IsActive=1).order_by("IsGroup")

        if len(child_nodes) > 0:
            for child_node in child_nodes:
                self.get_child_data(child_node, result)
        return result

    def get_child_data(self, parent_node, child_data):
        if parent_node:
            temp_serializer = ProjectTestCaseSerializer(instance=parent_node)
            temp_data = temp_serializer.data

            if parent_node.IsGroup == False:
                child_data.append(temp_data)
                return
            temp_data["children"] = list()
            child_nodes = models.ProjectTestCase.objects.get_children(parent_node.id).filter(IsActive=1).order_by(
                "IsGroup")

            if len(child_nodes) > 0:
                for child_node in child_nodes:
                    self.get_child_data(child_node, temp_data["children"])
                child_data.append(temp_data)
            else:
                child_data.append(temp_data)
                return
        else:
            return

    class Meta:
        model = models.ProjectTestCase
        exclude = ('CreationTime', 'IsActive', 'Desc', 'ExpectResult', 'Precondition', 'Priority', 'Module', 'RunTimes',
                   'UpdateTime', 'Scenes', 'automate', 'accessTest', 'automatedCompletion')
        read_only_fields = ('id',)


class ProjectTestCaseTagSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = {}
        return result

    class Meta:
        model = models.ProjectTestCaseTags
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseAttachmentSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = {}
        return result

    class Meta:
        model = models.ProjectTestCaseAttachment
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseIssueSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = {}
        return result

    class Meta:
        model = models.ProjectTestCaseIssue
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseKityMindTopicSerializer(serializers.ModelSerializer):
    text = serializers.CharField(source='Title')
    note = serializers.CharField(source='Desc')
    priority = serializers.IntegerField(source='Priority')
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    class Meta:
        model = models.ProjectTestCase
        exclude = ('IsActive', 'CreationTime', 'Status', 'Module', 'automate', 'automatedCompletion', 'RunTimes')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestCaseKityMindSerializer(serializers.ModelSerializer):
    data = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_data(self, obj):
        result = list()
        child_data = dict()
        child_data["data"] = dict()
        child_data["children"] = list()
        root_node = models.ProjectTestCase.objects.get(obj.id)
        all_cases = models.ProjectTestCase.objects.get_descendant(obj.id)
        self.get_child_data(root_node, child_data, all_cases)
        result.append(child_data)
        return result

    def get_child_data(self, parent_node, child_data, desccend_cases):
        if parent_node:
            temp_serializer = ProjectTestCaseKityMindTopicSerializer(instance=parent_node)
            temp_data = temp_serializer.data
            child_data["data"] = temp_data
            if parent_node.IsGroup:
                child_nodes = models.ProjectTestCase.objects.get_children(parent_node.id).filter(IsActive=1).order_by("IsGroup")
                # child_nodes = self.get_child_cases(parent_node.id,desccend_cases)
                for child_node in child_nodes:
                    temp_child_data = dict()
                    temp_child_data["data"] = dict()
                    temp_child_data["children"] = list()
                    self.get_child_data(child_node, temp_child_data, desccend_cases)
                    child_data["children"].append(temp_child_data)
            else:
                return child_data
        else:
            return child_data

    def get_child_cases(self, parent_id, desccend_cases):
        result = list()
        for case in desccend_cases:
            if case.Parent == parent_id:
                result.append(case)
        return result

    class Meta:
        model = models.ProjectTestCase
        exclude = ('IsActive', 'Status', 'Module', 'automate', 'automatedCompletion', 'RunTimes')
        read_only_fields = ('id',)
